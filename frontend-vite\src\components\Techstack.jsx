import { useState, useEffect } from 'react';
import { useProject } from '../hooks/useProject';
import { useNotification } from '../contexts/NotificationContext';

const Techstack = () => {
  const { currentProject } = useProject();
  const { showSuccess, showError } = useNotification();
  const [techstack, setTechstack] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [configExists, setConfigExists] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editedConfig, setEditedConfig] = useState(null);
  const [activeTab, setActiveTab] = useState('techstack');
  const [ignorePatterns, setIgnorePatterns] = useState([]);
  const [ignoreConfigExists, setIgnoreConfigExists] = useState(false);
  const [editingIgnore, setEditingIgnore] = useState(false);
  const [newIgnorePattern, setNewIgnorePattern] = useState('');

  useEffect(() => {
    if (currentProject?.path) {
      detectTechstack();
      loadIgnorePatterns();
    }
  }, [currentProject]);

  const detectTechstack = async () => {
    if (!currentProject?.path) return;

    setLoading(true);
    setError(null);

    try {
      // First load ignore patterns to ensure they're up to date
      await loadIgnorePatterns();

      const response = await fetch(`/api/techstack/detect?project_path=${encodeURIComponent(currentProject.path)}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setTechstack(data.detected);
      setConfigExists(data.config_exists);

      // Show notification that markdown file was updated
      if (data.markdown_path) {
        showSuccess('Techstack analysis updated and saved to techstack.md');
      }

      // If config exists, load it
      if (data.config_exists) {
        loadConfig();
      }
    } catch (err) {
      console.error('Error detecting techstack:', err);
      setError(err.message);
      showError('Failed to detect techstack');
    } finally {
      setLoading(false);
    }
  };

  const loadConfig = async () => {
    if (!currentProject?.path) return;

    try {
      const response = await fetch(`/api/techstack/config?project_path=${encodeURIComponent(currentProject.path)}`);
      
      if (response.ok) {
        const config = await response.json();
        setEditedConfig(config);
      }
    } catch (err) {
      console.error('Error loading config:', err);
    }
  };

  const saveConfig = async () => {
    if (!currentProject?.path || !editedConfig) return;

    try {
      const response = await fetch('/api/techstack/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_path: currentProject.path,
          config: editedConfig
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setConfigExists(true);
      setEditMode(false);
      showSuccess('Techstack configuration saved successfully');
    } catch (err) {
      console.error('Error saving config:', err);
      showError('Failed to save techstack configuration');
    }
  };

  const startEdit = () => {
    if (!editedConfig) {
      // Create initial config from detected techstack
      setEditedConfig({
        languages: techstack?.languages || [],
        frontend_frameworks: techstack?.frontend_frameworks || [],
        backend_frameworks: techstack?.backend_frameworks || [],
        build_tools: techstack?.build_tools || [],
        automation_tools: techstack?.automation_tools || [],
        databases: techstack?.databases || [],
        other_technologies: techstack?.other_technologies || [],
        project_structure: techstack?.project_structure || {},
        custom_notes: ''
      });
    }
    setEditMode(true);
  };

  const cancelEdit = () => {
    setEditMode(false);
    if (configExists) {
      loadConfig();
    }
  };

  const updateConfigField = (field, value) => {
    setEditedConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addToList = (field, item) => {
    if (item.trim() && !editedConfig[field].includes(item.trim())) {
      updateConfigField(field, [...editedConfig[field], item.trim()]);
    }
  };

  const removeFromList = (field, index) => {
    updateConfigField(field, editedConfig[field].filter((_, i) => i !== index));
  };

  const loadIgnorePatterns = async () => {
    if (!currentProject?.path) return;

    try {
      const response = await fetch(`/api/techstack/ignore?project_path=${encodeURIComponent(currentProject.path)}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setIgnorePatterns(data.patterns || []);
      setIgnoreConfigExists(data.config_exists);
    } catch (err) {
      console.error('Error loading ignore patterns:', err);
      showError('Failed to load ignore patterns');
    }
  };

  const saveIgnorePatterns = async () => {
    if (!currentProject?.path) return;

    try {
      const response = await fetch('/api/techstack/ignore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_path: currentProject.path,
          patterns: ignorePatterns
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      showSuccess('Ignore patterns saved successfully');
      setIgnoreConfigExists(true);
      setEditingIgnore(false);

      // Refresh techstack detection with new ignore patterns
      detectTechstack();
    } catch (err) {
      console.error('Error saving ignore patterns:', err);
      showError('Failed to save ignore patterns');
    }
  };

  const addIgnorePattern = () => {
    if (newIgnorePattern.trim() && !ignorePatterns.includes(newIgnorePattern.trim())) {
      setIgnorePatterns([...ignorePatterns, newIgnorePattern.trim()]);
      setNewIgnorePattern('');
    }
  };

  const removeIgnorePattern = (index) => {
    setIgnorePatterns(ignorePatterns.filter((_, i) => i !== index));
  };

  if (!currentProject) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-yellow-800">Please select a project to view its techstack.</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Detecting techstack...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">Error: {error}</p>
            <button
              onClick={detectTechstack}
              className="mt-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  const renderList = (title, items, field = null, editable = false) => (
    <div className="bg-white shadow rounded-lg p-4">
      <h3 className="text-base font-medium text-gray-900 mb-3">{title}</h3>
      {items && items.length > 0 ? (
        <div className="space-y-1">
          {items.map((item, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-50 px-2 py-1 rounded text-sm">
              <span className="text-gray-700">{item}</span>
              {editable && editMode && (
                <button
                  onClick={() => removeFromList(field, index)}
                  className="text-red-600 hover:text-red-800 text-xs"
                >
                  ×
                </button>
              )}
            </div>
          ))}
          {editable && editMode && (
            <div className="mt-2">
              <input
                type="text"
                placeholder={`Add ${title.toLowerCase()}`}
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addToList(field, e.target.value);
                    e.target.value = '';
                  }
                }}
              />
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500 text-sm">None detected</p>
      )}
    </div>
  );

  const displayData = editMode ? editedConfig : techstack;

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">Techstack</h1>
            <p className="text-gray-600 text-sm">
              {currentProject.name} - Technology stack analysis
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={detectTechstack}
              className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700"
            >
              Refresh Detection
            </button>
            {!editMode ? (
              <button
                onClick={startEdit}
                className="bg-green-600 text-white px-3 py-1.5 rounded text-sm hover:bg-green-700"
              >
                {configExists ? 'Edit Config' : 'Create Config'}
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={saveConfig}
                  className="bg-green-600 text-white px-3 py-1.5 rounded text-sm hover:bg-green-700"
                >
                  Save
                </button>
                <button
                  onClick={cancelEdit}
                  className="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-4">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('techstack')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'techstack'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Techstack
              </button>
              <button
                onClick={() => setActiveTab('ignore')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'ignore'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Ignore Patterns
              </button>
            </nav>
          </div>
        </div>

        {/* Status */}
        <div className="mb-4">
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
            configExists 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {configExists ? 'Configuration exists' : 'No configuration found'}
          </div>
          {editMode && (
            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 ml-2">
              Edit Mode
            </div>
          )}
        </div>

        {/* Tab Content */}
        {activeTab === 'techstack' && (
          <>
            {/* Techstack Grid - Responsive and Compact */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
          {renderList('Languages', displayData?.languages, 'languages', true)}
          {renderList('Frontend Frameworks', displayData?.frontend_frameworks, 'frontend_frameworks', true)}
          {renderList('Backend Frameworks', displayData?.backend_frameworks, 'backend_frameworks', true)}
          {renderList('Build Tools', displayData?.build_tools, 'build_tools', true)}
          {renderList('Automation Tools', displayData?.automation_tools, 'automation_tools', true)}
          {renderList('Databases', displayData?.databases, 'databases', true)}
          {renderList('Other Technologies', displayData?.other_technologies, 'other_technologies', true)}
        </div>

        {/* Project Structure - More Compact */}
        <div className="bg-white shadow rounded-lg p-4 mb-4">
          <h3 className="text-base font-medium text-gray-900 mb-3">Project Structure</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <h4 className="font-medium text-gray-700 mb-2 text-sm">Frontend Directories</h4>
              {displayData?.project_structure?.frontend_dirs?.length > 0 ? (
                <div className="space-y-1">
                  {displayData.project_structure.frontend_dirs.map((dir, index) => (
                    <div key={index} className="text-gray-600 bg-gray-50 px-2 py-1 rounded text-sm">{dir}</div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">None detected</p>
              )}
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2 text-sm">Backend Directories</h4>
              {displayData?.project_structure?.backend_dirs?.length > 0 ? (
                <div className="space-y-1">
                  {displayData.project_structure.backend_dirs.map((dir, index) => (
                    <div key={index} className="text-gray-600 bg-gray-50 px-2 py-1 rounded text-sm">{dir}</div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">None detected</p>
              )}
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2 text-sm">Config Directories</h4>
              {displayData?.project_structure?.config_dirs?.length > 0 ? (
                <div className="space-y-1">
                  {displayData.project_structure.config_dirs.map((dir, index) => (
                    <div key={index} className="text-gray-600 bg-gray-50 px-2 py-1 rounded text-sm">{dir}</div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">None detected</p>
              )}
            </div>
          </div>
        </div>

        {/* Custom Notes (only in edit mode) */}
        {editMode && (
          <div className="bg-white shadow rounded-lg p-4">
            <h3 className="text-base font-medium text-gray-900 mb-3">Custom Notes</h3>
            <textarea
              value={editedConfig?.custom_notes || ''}
              onChange={(e) => updateConfigField('custom_notes', e.target.value)}
              placeholder="Add any custom notes about the techstack..."
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
              rows={3}
            />
          </div>
        )}

            {/* Display custom notes if they exist */}
            {!editMode && displayData?.custom_notes && (
              <div className="bg-white shadow rounded-lg p-4">
                <h3 className="text-base font-medium text-gray-900 mb-3">Notes</h3>
                <p className="text-gray-700 whitespace-pre-wrap text-sm">{displayData.custom_notes}</p>
              </div>
            )}
          </>
        )}

        {/* Ignore Patterns Tab */}
        {activeTab === 'ignore' && (
          <div className="bg-white shadow rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-medium text-gray-900">Techstack Ignore Patterns</h3>
              <div className="flex space-x-2">
                {!editingIgnore ? (
                  <button
                    onClick={() => setEditingIgnore(true)}
                    className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700"
                  >
                    Edit Patterns
                  </button>
                ) : (
                  <>
                    <button
                      onClick={saveIgnorePatterns}
                      className="bg-green-600 text-white px-3 py-1.5 rounded text-sm hover:bg-green-700"
                    >
                      Save
                    </button>
                    <button
                      onClick={() => {
                        setEditingIgnore(false);
                        loadIgnorePatterns();
                      }}
                      className="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700"
                    >
                      Cancel
                    </button>
                  </>
                )}
              </div>
            </div>

            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs mb-4 ${
              ignoreConfigExists
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {ignoreConfigExists ? 'Custom ignore patterns configured' : 'Using default ignore patterns'}
            </div>

            <p className="text-gray-600 text-sm mb-4">
              Configure which files and directories to exclude from techstack analysis.
              These patterns are separate from MVCD ignore patterns.
            </p>

            {editingIgnore && (
              <div className="mb-4">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newIgnorePattern}
                    onChange={(e) => setNewIgnorePattern(e.target.value)}
                    placeholder="Add ignore pattern (e.g., *.log, temp/, node_modules/)"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addIgnorePattern();
                      }
                    }}
                  />
                  <button
                    onClick={addIgnorePattern}
                    className="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                  >
                    Add
                  </button>
                </div>
              </div>
            )}

            <div className="space-y-2">
              {ignorePatterns.map((pattern, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                  <span className="text-gray-700 font-mono text-sm">{pattern}</span>
                  {editingIgnore && (
                    <button
                      onClick={() => removeIgnorePattern(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
              {ignorePatterns.length === 0 && (
                <p className="text-gray-500 text-sm italic">No ignore patterns configured</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Techstack; 