"""
MVCD API Endpoints

This module provides API endpoints for the Minimum Viable Code Description (MVCD) workflow.
The workflow consists of three steps:
1. Analyze Codebase and Create MVCD
2. Enrich MVCD with Confidence Enhancement
3. Improvement Analysis
"""

import os
import sys
import json
import time
import threading
import re
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Add the project root to the Python path
sys.path.append(os.path.abspath("."))

# Import the MVCDService and MVCDEnrichmentService
from app.services.mvcd_service import MVCDService
from app.services.mvcd_enrichment import MVCDEnrichmentService

router = APIRouter()

# Models
class MVCDStatusResponse(BaseModel):
    """Response model for MVCD status"""
    mvcd_exists: bool
    last_updated: Optional[float] = None
    total_entries: int = 0
    entries_with_descriptions: int = 0
    average_confidence: float = 0
    frontend_entries: int = 0
    backend_entries: int = 0
    frontend_confidence: float = 0
    backend_confidence: float = 0
    total_loc: int = 0
    frontend_loc: int = 0
    backend_loc: int = 0

class MVCDGenerateRequest(BaseModel):
    """Request model for generating MVCD"""
    project_path: str
    refresh_only: bool = False  # Only add new files, don't regenerate existing ones
    force_recreate: bool = False  # Completely rebuild the MVCD file

class MVCDEnrichRequest(BaseModel):
    """Request model for enriching MVCD"""
    project_path: str
    coding_agent_type: str = "augment"  # Options: "cursor", "augment", "chatgpt"
    headless: bool = False
    timeout: int = 300
    prompt_type: str = "enrichment"  # Options: "enrichment", "execute_improvement"

class MVCDAnalyzeRequest(BaseModel):
    """Request model for analyzing MVCD for improvements"""
    project_path: str

class MVCDTaskResponse(BaseModel):
    """Response model for MVCD task status"""
    task_id: str
    status: str
    message: str

# Background tasks
running_tasks = {}

def _parse_markdown_improvements(file_path: Path, start_id: int) -> List[Dict[str, Any]]:
    """Parse improvement suggestions from a markdown file"""
    improvements = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Get file modification time as suggestion timestamp
        suggestion_timestamp = datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()

        # Parse markdown content - look for numbered lists or bullet points
        lines = content.split('\n')
        current_id = start_id

        for line in lines:
            line = line.strip()
            # Match numbered lists (1., 2., etc.) or bullet points (-, *, +)
            if re.match(r'^(\d+\.|[-*+])\s+(.+)', line):
                match = re.match(r'^(\d+\.|[-*+])\s+(.+)', line)
                if match:
                    description = match.group(2).strip()

                    # Try to extract file information from the description
                    file_match = re.search(r'`([^`]+\.(py|js|jsx|ts|tsx|java|cpp|c|h))`', description)
                    file_name = file_match.group(1) if file_match else "General"

                    # Determine category based on keywords
                    category = "general"
                    if any(keyword in description.lower() for keyword in ["refactor", "duplicate", "similar"]):
                        category = "refactor"
                    elif any(keyword in description.lower() for keyword in ["unused", "remove", "delete"]):
                        category = "cleanup"
                    elif any(keyword in description.lower() for keyword in ["performance", "optimize", "efficiency"]):
                        category = "performance"
                    elif any(keyword in description.lower() for keyword in ["clarity", "readable", "comment"]):
                        category = "clarity"

                    improvements.append({
                        "id": current_id,
                        "file": file_name,
                        "element": "",
                        "description": description,
                        "category": category,
                        "suggestion_timestamp": suggestion_timestamp,
                        "priority": None,
                        "done_timestamp": None,
                        "source_file": str(file_path.name)
                    })
                    current_id += 1

    except Exception as e:
        print(f"Error parsing markdown file {file_path}: {e}")

    return improvements

def _parse_yaml_improvements(file_path: Path, start_id: int) -> List[Dict[str, Any]]:
    """Parse improvement suggestions from a YAML file"""
    improvements = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            yaml_content = yaml.safe_load(f)

        # Get file modification time as suggestion timestamp
        suggestion_timestamp = datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()

        if isinstance(yaml_content, list):
            current_id = start_id
            for item in yaml_content:
                if isinstance(item, dict):
                    # Build description from issue and suggestion fields
                    issue = item.get("issue", "")
                    suggestion = item.get("suggestion", "")

                    # Combine issue and suggestion for description
                    if issue and suggestion:
                        description = f"{issue} Suggestion: {suggestion}"
                    elif suggestion:
                        description = suggestion
                    elif issue:
                        description = issue
                    else:
                        description = "No description available"

                    improvements.append({
                        "id": current_id,
                        "file": item.get("file", "Unknown"),
                        "element": item.get("element", ""),
                        "description": description,
                        "category": item.get("category", "general"),
                        "suggestion_timestamp": suggestion_timestamp,
                        "priority": item.get("priority"),
                        "done_timestamp": item.get("done_timestamp"),
                        "source_file": str(file_path.name),
                        "confidence": item.get("confidence"),
                        "impact_level": item.get("impact_level"),
                        "line": item.get("line"),
                        "issue_id": item.get("issue_id", ""),
                        "issue": issue,
                        "suggestion": suggestion
                    })
                    current_id += 1

    except Exception as e:
        print(f"Error parsing YAML file {file_path}: {e}")

    return improvements

def _load_improvement_tracking(project_root: Path) -> Dict[str, Any]:
    """Load improvement tracking data"""
    tracking_file = project_root / ".VibeArch" / "Improvement" / "improvement_tracking.json"

    if tracking_file.exists():
        try:
            with open(tracking_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            pass

    return {}

def get_mvcd_path(project_path: str) -> Path:
    """Get the path to the MVCD file for a project"""
    project_root = Path(project_path).resolve()
    return project_root / ".VibeArch" / "Directory" / "mvcd.yaml"

def calculate_mvcd_metrics(project_path: str) -> Dict[str, Any]:
    """Calculate metrics from the MVCD file"""
    try:
        mvcd_service = MVCDService(project_path)
        entries = mvcd_service.load_existing_mvcd()

        if not entries:
            return {
                "mvcd_exists": False,
                "total_entries": 0,
                "entries_with_descriptions": 0,
                "average_confidence": 0,
                "frontend_entries": 0,
                "backend_entries": 0,
                "frontend_confidence": 0,
                "backend_confidence": 0,
                "total_loc": 0,
                "frontend_loc": 0,
                "backend_loc": 0
            }

        # Calculate metrics
        total_entries = len(entries)
        entries_with_descriptions = sum(1 for entry in entries if entry.get("description") and entry.get("description") != "TODO: Add description")

        # Calculate confidence metrics
        confidences = [entry.get("confidence", 0) for entry in entries if entry.get("confidence") is not None]
        average_confidence = sum(confidences) / len(confidences) if confidences else 0

        # Calculate frontend/backend metrics
        frontend_entries = [entry for entry in entries if "frontend" in entry.get("file", "").lower()]
        backend_entries = [entry for entry in entries if "backend" in entry.get("file", "").lower()]

        frontend_confidences = [entry.get("confidence", 0) for entry in frontend_entries if entry.get("confidence") is not None]
        backend_confidences = [entry.get("confidence", 0) for entry in backend_entries if entry.get("confidence") is not None]

        frontend_confidence = sum(frontend_confidences) / len(frontend_confidences) if frontend_confidences else 0
        backend_confidence = sum(backend_confidences) / len(backend_confidences) if backend_confidences else 0

        # Calculate LOC metrics
        total_loc = sum(entry.get("loc", 0) for entry in entries)
        frontend_loc = sum(entry.get("loc", 0) for entry in frontend_entries)
        backend_loc = sum(entry.get("loc", 0) for entry in backend_entries)

        # Get last updated time
        mvcd_path = get_mvcd_path(project_path)
        last_updated = os.path.getmtime(mvcd_path) if mvcd_path.exists() else None

        return {
            "mvcd_exists": True,
            "last_updated": last_updated,
            "total_entries": total_entries,
            "entries_with_descriptions": entries_with_descriptions,
            "average_confidence": average_confidence,
            "frontend_entries": len(frontend_entries),
            "backend_entries": len(backend_entries),
            "frontend_confidence": frontend_confidence,
            "backend_confidence": backend_confidence,
            "total_loc": total_loc,
            "frontend_loc": frontend_loc,
            "backend_loc": backend_loc
        }
    except Exception as e:
        print(f"Error calculating MVCD metrics: {e}")
        return {
            "mvcd_exists": False,
            "total_entries": 0,
            "entries_with_descriptions": 0,
            "average_confidence": 0,
            "frontend_entries": 0,
            "backend_entries": 0,
            "frontend_confidence": 0,
            "backend_confidence": 0,
            "total_loc": 0,
            "frontend_loc": 0,
            "backend_loc": 0
        }

@router.get("/status")
async def get_mvcd_status(project_path: str = Query(..., description="Path to the project root")):
    """Get the status of the MVCD for a project"""
    try:
        metrics = calculate_mvcd_metrics(project_path)
        return JSONResponse(content=metrics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting MVCD status: {str(e)}")

@router.get("/content")
async def get_mvcd_content(project_path: str = Query(..., description="Path to the project root")):
    """Get the content of the MVCD file for a project"""
    try:
        mvcd_path = get_mvcd_path(project_path)

        if not mvcd_path.exists():
            raise HTTPException(status_code=404, detail="MVCD file not found")

        # Read the MVCD file
        with open(mvcd_path, "r", encoding="utf-8") as f:
            content = f.read()

        return JSONResponse(content={"content": content})
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting MVCD content: {str(e)}")

@router.get("/ignore")
async def get_ignore_content(project_path: str = Query(..., description="Path to the project root")):
    """Get the content of the .mvcd-ignore.yaml file for a project"""
    try:
        project_root = Path(project_path).resolve()
        ignore_path = project_root / ".VibeArch" / "VibeArch_Setup" / ".mvcd-ignore.yaml"

        if not ignore_path.exists():
            raise HTTPException(status_code=404, detail="Ignore file not found")

        with open(ignore_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return JSONResponse(content={
            "content": content,
            "path": str(ignore_path)
        })
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading ignore file: {str(e)}")

@router.post("/ignore")
async def save_ignore_content(request: dict):
    """Save the content of the .mvcd-ignore.yaml file for a project"""
    try:
        project_path = request.get("project_path")
        content = request.get("content")

        if not project_path or not content:
            raise HTTPException(status_code=400, detail="Missing project_path or content")

        project_root = Path(project_path).resolve()
        ignore_path = project_root / ".VibeArch" / "VibeArch_Setup" / ".mvcd-ignore.yaml"

        # Ensure the directory exists
        ignore_path.parent.mkdir(parents=True, exist_ok=True)

        with open(ignore_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return JSONResponse(content={
            "success": True,
            "path": str(ignore_path),
            "message": "Ignore file saved successfully"
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving ignore file: {str(e)}")

@router.get("/improvements/files")
async def get_improvement_files(project_path: str = Query(..., description="Path to the project root")):
    """Get list of available improvement files"""
    try:
        project_root = Path(project_path).resolve()
        improvement_dir = project_root / ".VibeArch" / "Improvement"

        if not improvement_dir.exists():
            return JSONResponse(content={"files": []})

        files = []
        for file_path in improvement_dir.iterdir():
            if file_path.is_file() and file_path.name != "improvement_tracking.json":
                if file_path.suffix in ['.md', '.yaml', '.yml']:
                    files.append({
                        "name": file_path.name,
                        "path": str(file_path.relative_to(project_root)),
                        "last_modified": file_path.stat().st_mtime,
                        "size": file_path.stat().st_size
                    })

        # Sort files by last modified (newest first)
        files.sort(key=lambda x: x["last_modified"], reverse=True)

        return JSONResponse(content={"files": files})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading improvement files: {str(e)}")

@router.get("/improvements")
async def get_improvements(
    project_path: str = Query(..., description="Path to the project root"),
    file_name: str = Query(None, description="Specific improvement file to load")
):
    """Get improvement suggestions from the .VibeArch/Improvement directory"""
    try:
        project_root = Path(project_path).resolve()
        improvement_dir = project_root / ".VibeArch" / "Improvement"

        if not improvement_dir.exists():
            return JSONResponse(content={"improvements": []})

        improvements = []
        improvement_id = 1

        # If specific file is requested, process only that file
        if file_name:
            file_path = improvement_dir / file_name
            if file_path.exists() and file_path.is_file():
                if file_path.suffix == '.md':
                    improvements.extend(_parse_markdown_improvements(file_path, improvement_id))
                elif file_path.suffix in ['.yaml', '.yml']:
                    improvements.extend(_parse_yaml_improvements(file_path, improvement_id))
        else:
            # Process all files in the improvement directory (skip tracking file)
            for file_path in improvement_dir.iterdir():
                if file_path.is_file() and file_path.name != "improvement_tracking.json":
                    if file_path.suffix == '.md':
                        # Parse markdown file
                        improvements.extend(_parse_markdown_improvements(file_path, improvement_id))
                    elif file_path.suffix in ['.yaml', '.yml']:
                        # Parse YAML file
                        improvements.extend(_parse_yaml_improvements(file_path, improvement_id))
                    improvement_id += len(improvements)

        # Load tracking data and merge with improvements
        tracking_data = _load_improvement_tracking(project_root)

        for improvement in improvements:
            improvement_id_str = str(improvement["id"])
            if improvement_id_str in tracking_data:
                tracking_info = tracking_data[improvement_id_str]
                improvement["priority"] = tracking_info.get("priority")
                improvement["done_timestamp"] = tracking_info.get("done_timestamp")

        return JSONResponse(content={"improvements": improvements})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading improvements: {str(e)}")

@router.post("/improvements")
async def update_improvement(request: dict):
    """Update an improvement's priority or status"""
    try:
        project_path = request.get("project_path")
        improvement_id = request.get("improvement_id")
        priority = request.get("priority")
        done_timestamp = request.get("done_timestamp")

        if not project_path or improvement_id is None:
            raise HTTPException(status_code=400, detail="Missing project_path or improvement_id")

        # For now, we'll store updates in a separate tracking file
        # This allows us to maintain priorities without modifying the original improvement files
        project_root = Path(project_path).resolve()
        tracking_file = project_root / ".VibeArch" / "Improvement" / "improvement_tracking.json"

        # Load existing tracking data
        tracking_data = {}
        if tracking_file.exists():
            with open(tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)

        # Update the improvement
        if str(improvement_id) not in tracking_data:
            tracking_data[str(improvement_id)] = {}

        if priority is not None:
            tracking_data[str(improvement_id)]["priority"] = priority
        if done_timestamp is not None:
            tracking_data[str(improvement_id)]["done_timestamp"] = done_timestamp

        # Ensure the directory exists
        tracking_file.parent.mkdir(parents=True, exist_ok=True)

        # Save tracking data
        with open(tracking_file, 'w', encoding='utf-8') as f:
            json.dump(tracking_data, f, indent=2)

        return JSONResponse(content={
            "success": True,
            "message": "Improvement updated successfully"
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating improvement: {str(e)}")

@router.post("/execute-improvement")
async def execute_improvement(request: dict):
    """Update the execute_selected_improvement.yaml file with improvement details"""
    try:
        project_path = request.get("project_path")
        content = request.get("content")
        improvement_data = request.get("improvement_data")

        if not project_path or not content:
            raise HTTPException(status_code=400, detail="Missing project_path or content")

        project_root = Path(project_path).resolve()
        execute_file_path = project_root / ".VibeArch" / "VibeArch_Setup" / "execute_selected_improvement.yaml"

        # Ensure the directory exists
        execute_file_path.parent.mkdir(parents=True, exist_ok=True)

        # Write the improvement prompt content
        with open(execute_file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return JSONResponse(content={
            "success": True,
            "path": str(execute_file_path),
            "message": "Execute improvement prompt updated successfully",
            "improvement_data": improvement_data
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating execute improvement prompt: {str(e)}")

@router.post("/generate", response_model=MVCDTaskResponse)
async def generate_mvcd(request: MVCDGenerateRequest, background_tasks: BackgroundTasks):
    """Generate the initial MVCD structure for a project"""
    task_id = f"generate_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Generating MVCD..."}

    def run_mvcd_generation(project_path: str, refresh_only: bool = False, force_recreate: bool = False):
        try:
            mvcd_service = MVCDService(project_path)
            
            if force_recreate:
                # Delete existing MVCD file to force complete recreation
                mvcd_path = get_mvcd_path(project_path)
                if mvcd_path.exists():
                    mvcd_path.unlink()
                running_tasks[task_id] = {"status": "running", "message": "Recreating MVCD (complete rebuild)..."}
                mvcd_service.generate()
            elif refresh_only:
                # Only add new files, preserve existing entries
                running_tasks[task_id] = {"status": "running", "message": "Refreshing MVCD (adding new files only)..."}
                mvcd_service.generate_refresh_only()
            else:
                # Standard generation
                running_tasks[task_id] = {"status": "running", "message": "Generating MVCD..."}
                mvcd_service.generate()
                
            running_tasks[task_id] = {"status": "completed", "message": "MVCD generation completed successfully"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD generation failed: {str(e)}"}

    background_tasks.add_task(run_mvcd_generation, request.project_path, request.refresh_only, request.force_recreate)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD generation started"
    )

@router.post("/enrich", response_model=MVCDTaskResponse)
async def enrich_mvcd(request: MVCDEnrichRequest, background_tasks: BackgroundTasks):
    """Enrich the MVCD with descriptions and confidence scores using a coding agent"""
    task_id = f"enrich_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Enriching MVCD..."}

    def run_mvcd_enrichment(project_path: str, config: Dict[str, Any]):
        try:
            os.chdir(project_path)

            def get_agent_scripts(agent_type: str) -> List[str]:
                """Get the appropriate AutoHotkey scripts for the specified coding agent"""
                script_mappings = {
                    "augment": [
                        "improved_augment.ahk",
                        "just_paste.ahk",
                        "multi_approach.ahk",
                        "ultra_minimal.ahk",
                        "no_window_enum.ahk",
                        "absolute_minimal.ahk"
                    ],
                    "cursor": [
                        "simple_cursor.ahk",          # Try the simple script first
                        "improved_cursor.ahk",
                        "cursor_prompt.ahk",
                        "just_paste.ahk",
                        "ultra_minimal.ahk"
                    ],
                    "openai": [
                        "openai_automation.ahk",  # Future script for OpenAI
                        "just_paste.ahk",
                        "ultra_minimal.ahk"
                    ],
                    "chatgpt": [
                        "chatgpt_automation.ahk",  # Future script for ChatGPT
                        "just_paste.ahk",
                        "ultra_minimal.ahk"
                    ]
                }
                
                # Return scripts for the agent, or fallback to generic scripts
                agent_scripts = script_mappings.get(agent_type.lower(), ["just_paste.ahk", "ultra_minimal.ahk"])
                
                # Convert to full paths
                return [os.path.join(project_path, "scripts", script) for script in agent_scripts]

            if config["coding_agent_type"].lower() in ["augment", "cursor", "openai", "chatgpt"]:
                agent_type = config["coding_agent_type"].lower()
                running_tasks[task_id] = {"status": "running", "message": f"Using {agent_type.title()} automation..."}

                import subprocess
                import threading

                # Create a debug log file in a location that's definitely writable
                debug_log_path = os.path.join(project_path, "mvcd_debug.log")
                with open(debug_log_path, "w") as f:
                    f.write(f"MVCD Enrichment Debug Log - {agent_type.title()} Workflow - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Project path: {project_path}\n")
                    f.write(f"Coding agent: {config['coding_agent_type']}\n")
                    f.write(f"Starting {agent_type.title()} automation...\n")

                    # Check if the PowerShell script exists (for Augment and Cursor)
                    ps_script_paths = []
                    if agent_type == "augment":
                        ps_script_paths = [
                            os.path.join(project_path, "scripts", "run_mvcd_augment.ps1")
                        ]
                    elif agent_type == "cursor":
                        ps_script_paths = [
                            os.path.join(project_path, "scripts", "run_mvcd_cursor_simple.ps1"),
                            os.path.join(project_path, "scripts", "run_mvcd_cursor.ps1")
                        ]
                    
                    ps_script_path = None
                    for path in ps_script_paths:
                        if os.path.exists(path):
                            ps_script_path = path
                            break
                    
                    if ps_script_path and os.path.exists(ps_script_path):
                        # Use the PowerShell script for the agent
                        with open(debug_log_path, "a") as f:
                            f.write(f"Using PowerShell script: {ps_script_path}\n")

                        try:
                            running_tasks[task_id] = {
                                "status": "running",
                                "message": f"Running PowerShell script: {os.path.basename(ps_script_path)}..."
                            }

                            # Get project name for PowerShell script
                            project_name = os.path.basename(project_path)
                            
                            with open(debug_log_path, "a") as f:
                                f.write(f"Project name parameter: {project_name}\n")
                                f.write(f"Executing PowerShell command: powershell -ExecutionPolicy Bypass -File {ps_script_path} '{project_name}'\n")

                            # Run the PowerShell script with project name parameter
                            result = subprocess.run(
                                ["powershell", "-ExecutionPolicy", "Bypass", "-File", ps_script_path, project_name],
                                capture_output=True,
                                text=True,
                                check=True
                            )

                            # Log the output
                            with open(debug_log_path, "a") as f:
                                f.write("PowerShell script output:\n")
                                f.write(result.stdout)
                                if result.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(result.stderr)

                            # Mark the task as completed after prompt is inserted
                            running_tasks[task_id] = {
                                "status": "completed",
                                "message": f"Prompt inserted into {agent_type.title()} chat. Please check {agent_type.title()} for the response and wait for it to complete."
                            }
                            return
                        except subprocess.CalledProcessError as e:
                            error_msg = f"PowerShell script failed: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")
                                if e.stdout:
                                    f.write("Output:\n")
                                    f.write(e.stdout)
                                if e.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(e.stderr)

                            # Fall through to AutoHotkey fallback
                        except Exception as e:
                            error_msg = f"Error running PowerShell script: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")
                            # Fall through to AutoHotkey fallback

                    # For all agents (including Augment fallback), use AutoHotkey scripts
                    # Define possible AutoHotKey paths
                    ahk_paths = [
                        r"C:\Program Files\AutoHotkey\v2\AutoHotkey64.exe",
                        r"C:\Program Files\AutoHotkey\v2\AutoHotkey.exe",
                        "AutoHotkey.exe"  # Try PATH as last resort
                    ]

                    # Find the first path that exists
                    ahk_path = None
                    for path in ahk_paths:
                        if os.path.exists(path):
                            ahk_path = path
                            with open(debug_log_path, "a") as f:
                                f.write(f"Found AutoHotKey at: {path}\n")
                            break
                        else:
                            with open(debug_log_path, "a") as f:
                                f.write(f"AutoHotKey not found at: {path}\n")

                    # If no path was found, try using the PATH environment variable
                    if not ahk_path:
                        with open(debug_log_path, "a") as f:
                            f.write(f"No AutoHotKey found in expected locations. Trying PATH...\n")

                        try:
                            result = subprocess.run(["where", "AutoHotkey.exe"],
                                                  capture_output=True,
                                                  text=True)
                            if result.returncode == 0:
                                ahk_path = "AutoHotkey.exe"
                                with open(debug_log_path, "a") as f:
                                    f.write(f"AutoHotKey found in PATH: {result.stdout.strip()}\n")
                            else:
                                with open(debug_log_path, "a") as f:
                                    f.write(f"AutoHotKey not found in PATH\n")
                        except Exception as e:
                            with open(debug_log_path, "a") as f:
                                f.write(f"Error checking PATH for AutoHotKey: {str(e)}\n")

                    # Get agent-specific scripts
                    script_paths = get_agent_scripts(agent_type)

                    # Find the first script that exists
                    script_to_use = None
                    for script_path in script_paths:
                        if os.path.exists(script_path):
                            script_to_use = script_path
                            break

                    with open(debug_log_path, "a") as f:
                        f.write(f"Using AutoHotKey path: {ahk_path}\n")
                        f.write(f"Using script: {script_to_use}\n")
                        f.write(f"Available scripts for {agent_type}: {script_paths}\n")

                    if not script_to_use:
                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"No {agent_type.title()} automation scripts found. Please manually open {agent_type.title()}, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter."
                        }
                        return

                    try:
                        running_tasks[task_id] = {
                            "status": "running",
                            "message": f"Running {agent_type.title()} AutoHotKey script: {os.path.basename(script_to_use)}"
                        }

                        # Create command
                        if ahk_path == "AutoHotkey.exe":
                            script_abs_path = os.path.abspath(script_to_use)
                            command = [ahk_path, script_abs_path]
                        else:
                            command = [ahk_path, script_to_use]

                        # Add project name as parameter for project-specific window targeting
                        project_name = os.path.basename(project_path)
                        command.append(project_name)

                        with open(debug_log_path, "a") as f:
                            f.write(f"Project name parameter: {project_name}\n")
                            f.write(f"Final command: {command}\n")

                        # Run the command
                        result = subprocess.run(command,
                                              capture_output=True,
                                              text=True,
                                              check=True)

                        # Log the output
                        with open(debug_log_path, "a") as f:
                            f.write(f"{agent_type.title()} AutoHotKey script output:\n")
                            if result.stdout:
                                f.write(result.stdout)
                            if result.stderr:
                                f.write("\nErrors:\n")
                                f.write(result.stderr)

                        # Mark the task as completed after prompt is inserted
                        running_tasks[task_id] = {
                            "status": "completed",
                            "message": f"Prompt inserted into {agent_type.title()} chat. Please check {agent_type.title()} for the response and wait for it to complete."
                        }
                    except subprocess.CalledProcessError as e:
                        error_msg = f"{agent_type.title()} AutoHotKey script failed: {str(e)}"
                        with open(debug_log_path, "a") as f:
                            f.write(f"ERROR: {error_msg}\n")
                            if hasattr(e, 'stdout') and e.stdout:
                                f.write("Output:\n")
                                f.write(e.stdout)
                            if hasattr(e, 'stderr') and e.stderr:
                                f.write("\nErrors:\n")
                                f.write(e.stderr)

                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"{agent_type.title()} AutoHotKey script failed. Please manually open {agent_type.title()}, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                        }
                        return
                    except Exception as e:
                        error_msg = f"Error running {agent_type.title()} AutoHotKey script: {str(e)}"
                        with open(debug_log_path, "a") as f:
                            f.write(f"ERROR: {error_msg}\n")

                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"Error running {agent_type.title()} AutoHotKey script. Please manually open {agent_type.title()}, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                        }
                        return

                return

            # For other coding agents, use the enrichment service
            enrichment_service = MVCDEnrichmentService(config)
            success = enrichment_service.enrich(config.get("prompt_type", "enrichment"))

            if success:
                running_tasks[task_id] = {"status": "completed", "message": "MVCD enrichment completed successfully"}
            else:
                running_tasks[task_id] = {"status": "failed", "message": "MVCD enrichment failed"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD enrichment failed: {str(e)}"}

    # Create config for the enrichment service
    config = {
        "coding_agent_type": request.coding_agent_type,
        "headless": request.headless,
        "timeout": request.timeout,
        "prompt_type": request.prompt_type,
        "prompt_path": ".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml",
        "output_path": ".VibeArch/Directory/mvcd.yaml",
        "retries": 3,
        "retry_delay": 5,
        "debug": False
    }

    background_tasks.add_task(run_mvcd_enrichment, request.project_path, config)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD enrichment started"
    )

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get the status of a running MVCD task"""
    if task_id not in running_tasks:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return JSONResponse(content={
        "task_id": task_id,
        **running_tasks[task_id]
    })

@router.post("/task/{task_id}/reset")
async def reset_task(task_id: str):
    """Reset a task that might be stuck"""
    if task_id not in running_tasks:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    # Update the task status to failed
    running_tasks[task_id] = {
        "status": "failed",
        "message": "Task was manually reset by user"
    }

    return JSONResponse(content={
        "task_id": task_id,
        **running_tasks[task_id],
        "reset": True
    })

@router.post("/analyze", response_model=MVCDTaskResponse)
async def analyze_mvcd(request: MVCDAnalyzeRequest, background_tasks: BackgroundTasks):
    """Analyze the MVCD for improvement opportunities"""
    task_id = f"analyze_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Analyzing MVCD..."}

    def run_mvcd_analysis(project_path: str):
        try:
            # TODO: Implement MVCD analysis logic
            # This would analyze the MVCD file and generate improvement suggestions
            # For now, we'll just simulate a successful analysis

            # Create the Improvement directory if it doesn't exist
            improvement_dir = Path(project_path) / ".VibeArch" / "Improvement"
            improvement_dir.mkdir(parents=True, exist_ok=True)

            # Create a placeholder improvement file
            with open(improvement_dir / "improvement_suggestions.md", "w") as f:
                f.write("# MVCD Improvement Suggestions\n\n")
                f.write("This file contains suggestions for improving the codebase based on MVCD analysis.\n\n")
                f.write("## Suggestions\n\n")
                f.write("1. Add more detailed descriptions to components with low confidence scores\n")
                f.write("2. Consider refactoring components with high LOC counts\n")
                f.write("3. Review dependencies for potential optimization\n")

            running_tasks[task_id] = {"status": "completed", "message": "MVCD analysis completed successfully"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD analysis failed: {str(e)}"}

    background_tasks.add_task(run_mvcd_analysis, request.project_path)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD analysis started"
    )
