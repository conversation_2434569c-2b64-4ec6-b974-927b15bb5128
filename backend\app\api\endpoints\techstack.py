"""
Techstack API Endpoints

This module provides API endpoints for detecting and managing project techstack information.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

class TechstackResponse(BaseModel):
    """Response model for techstack detection"""
    detected: Dict[str, Any]
    config_exists: bool
    config_path: Optional[str] = None

class TechstackSaveRequest(BaseModel):
    """Request model for saving techstack configuration"""
    project_path: str
    config: Dict[str, Any]

def detect_languages(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect programming languages used in the project"""
    languages = set()
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Check for common language indicators
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        for file in files:
            file_path = root_path / file
            if should_ignore_for_techstack(file_path, project_path, ignore_patterns):
                continue

            ext = Path(file).suffix.lower()
            if ext == '.py':
                languages.add('Python')
            elif ext in ['.js', '.jsx']:
                languages.add('JavaScript')
            elif ext in ['.ts', '.tsx']:
                languages.add('TypeScript')
            elif ext in ['.go']:
                languages.add('Go')
            elif ext in ['.rs']:
                languages.add('Rust')
            elif ext in ['.java']:
                languages.add('Java')
            elif ext in ['.php']:
                languages.add('PHP')
            elif ext in ['.rb']:
                languages.add('Ruby')
            elif ext in ['.cs']:
                languages.add('C#')
            elif ext in ['.cpp', '.cc', '.cxx']:
                languages.add('C++')
            elif ext == '.c':
                languages.add('C')
            elif ext in ['.kt', '.kts']:
                languages.add('Kotlin')
            elif ext in ['.swift']:
                languages.add('Swift')
            elif ext in ['.dart']:
                languages.add('Dart')
            elif ext in ['.scala']:
                languages.add('Scala')
            elif ext in ['.clj', '.cljs']:
                languages.add('Clojure')
            elif ext in ['.hs']:
                languages.add('Haskell')
            elif ext in ['.elm']:
                languages.add('Elm')
            elif ext in ['.r', '.R']:
                languages.add('R')
            elif ext in ['.m']:
                languages.add('Objective-C')
            elif ext in ['.lua']:
                languages.add('Lua')
            elif ext in ['.pl', '.pm']:
                languages.add('Perl')
            elif ext in ['.vim']:
                languages.add('Vimscript')

    return sorted(list(languages))

def detect_frontend_frameworks(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect frontend frameworks from package.json files"""
    frameworks = []
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Search for package.json files in the project and subdirectories
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        if 'package.json' in files:
            package_json_path = root_path / 'package.json'
            if should_ignore_for_techstack(package_json_path, project_path, ignore_patterns):
                continue
            package_json_path = Path(root) / 'package.json'
            try:
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)
                
                dependencies = {}
                dependencies.update(package_data.get('dependencies', {}))
                dependencies.update(package_data.get('devDependencies', {}))
                
                # Check for common frameworks
                if 'react' in dependencies:
                    frameworks.append('React')
                if 'vue' in dependencies:
                    frameworks.append('Vue')
                if '@angular/core' in dependencies:
                    frameworks.append('Angular')
                if 'svelte' in dependencies:
                    frameworks.append('Svelte')
                if 'next' in dependencies:
                    frameworks.append('Next.js')
                if 'nuxt' in dependencies:
                    frameworks.append('Nuxt.js')
                if 'gatsby' in dependencies:
                    frameworks.append('Gatsby')
                if 'remix' in dependencies:
                    frameworks.append('Remix')
                if 'astro' in dependencies:
                    frameworks.append('Astro')
                if 'solid-js' in dependencies:
                    frameworks.append('SolidJS')
                if 'preact' in dependencies:
                    frameworks.append('Preact')
                if 'lit' in dependencies or 'lit-element' in dependencies:
                    frameworks.append('Lit')
                if 'stencil' in dependencies:
                    frameworks.append('Stencil')
                
                # Check for build tools and other frontend tech
                if 'http-server' in dependencies:
                    frameworks.append('Static HTML')
                if 'vite' in dependencies:
                    frameworks.append('Vite')
                if 'webpack' in dependencies:
                    frameworks.append('Webpack')
                if 'parcel' in dependencies:
                    frameworks.append('Parcel')
                if 'rollup' in dependencies:
                    frameworks.append('Rollup')
                if 'esbuild' in dependencies:
                    frameworks.append('ESBuild')
                    
            except Exception as e:
                print(f"Error reading package.json at {package_json_path}: {e}")
    
    return list(set(frameworks))  # Remove duplicates

def detect_backend_frameworks(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect backend frameworks from requirements.txt and other files"""
    frameworks = []
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Search for requirements.txt files in the project and subdirectories
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        if 'requirements.txt' in files:
            requirements_path = root_path / 'requirements.txt'
            if should_ignore_for_techstack(requirements_path, project_path, ignore_patterns):
                continue
            requirements_path = Path(root) / 'requirements.txt'
            try:
                with open(requirements_path, 'r') as f:
                    requirements = f.read().lower()
                
                # Python frameworks
                if 'fastapi' in requirements:
                    frameworks.append('FastAPI')
                if 'django' in requirements:
                    frameworks.append('Django')
                if 'flask' in requirements:
                    frameworks.append('Flask')
                if 'tornado' in requirements:
                    frameworks.append('Tornado')
                if 'pyramid' in requirements:
                    frameworks.append('Pyramid')
                if 'bottle' in requirements:
                    frameworks.append('Bottle')
                if 'cherrypy' in requirements:
                    frameworks.append('CherryPy')
                if 'uvicorn' in requirements:
                    frameworks.append('Uvicorn')
                if 'gunicorn' in requirements:
                    frameworks.append('Gunicorn')
                
                # AI/ML frameworks
                if 'openai' in requirements:
                    frameworks.append('OpenAI')
                if 'anthropic' in requirements:
                    frameworks.append('Anthropic')
                if 'tensorflow' in requirements:
                    frameworks.append('TensorFlow')
                if 'pytorch' in requirements or 'torch' in requirements:
                    frameworks.append('PyTorch')
                if 'scikit-learn' in requirements or 'sklearn' in requirements:
                    frameworks.append('Scikit-learn')
                if 'pandas' in requirements:
                    frameworks.append('Pandas')
                if 'numpy' in requirements:
                    frameworks.append('NumPy')
                
                # Web scraping/automation
                if 'firecrawl' in requirements:
                    frameworks.append('Firecrawl')
                if 'selenium' in requirements:
                    frameworks.append('Selenium')
                if 'beautifulsoup' in requirements or 'bs4' in requirements:
                    frameworks.append('BeautifulSoup')
                if 'scrapy' in requirements:
                    frameworks.append('Scrapy')
                if 'requests' in requirements:
                    frameworks.append('Requests')
                    
            except Exception as e:
                print(f"Error reading requirements.txt at {requirements_path}: {e}")
    
    # Check for other framework indicators in project root
    if (project_path / "go.mod").exists():
        frameworks.append('Go')
    if (project_path / "Cargo.toml").exists():
        frameworks.append('Rust')
    if (project_path / "pom.xml").exists() or (project_path / "build.gradle").exists():
        frameworks.append('Java/Spring')
    if (project_path / "composer.json").exists():
        frameworks.append('PHP/Composer')
    if (project_path / "Gemfile").exists():
        frameworks.append('Ruby/Bundler')
    
    return list(set(frameworks))  # Remove duplicates

def detect_build_tools(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect build tools and bundlers"""
    tools = []
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Search for config files in the project and subdirectories
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        for file in files:
            file_path = root_path / file
            if should_ignore_for_techstack(file_path, project_path, ignore_patterns):
                continue
            if file in ['vite.config.js', 'vite.config.ts']:
                tools.append('Vite')
            elif file == 'webpack.config.js':
                tools.append('Webpack')
            elif file == 'rollup.config.js':
                tools.append('Rollup')
            elif file == 'tsconfig.json':
                tools.append('TypeScript')
            elif file == 'Dockerfile':
                tools.append('Docker')
            elif file == 'docker-compose.yml':
                tools.append('Docker Compose')
            elif file == 'package.json':
                tools.append('npm')
            elif file == 'requirements.txt':
                tools.append('pip')
    
    return list(set(tools))  # Remove duplicates

def detect_automation_tools(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect automation tools and scripts"""
    tools = []
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Search for automation files in the project and subdirectories
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        for file in files:
            file_path = root_path / file
            should_ignore = should_ignore_for_techstack(file_path, project_path, ignore_patterns)

            if file.endswith('.ahk'):
                # Force ignore all .ahk files regardless of pattern matching
                print(f"DEBUG: Found .ahk file: {file_path} - FORCING IGNORE")
                # Don't add to tools list
            elif file.endswith('.ps1'):
                if not should_ignore:
                    tools.append('PowerShell')
            elif file.endswith('.bat') or file.endswith('.cmd'):
                if not should_ignore:
                    tools.append('Batch Scripts')
            elif file.endswith('.sh'):
                if not should_ignore:
                    tools.append('Shell Scripts')
    
    # Check if AutoHotkey is installed on the system
    import subprocess
    try:
        ahk_paths = [
            "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey64.exe",
            "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey.exe",
            "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe"
        ]
        
        for path in ahk_paths:
            if Path(path).exists():
                if 'AutoHotkey' not in tools:
                    tools.append('AutoHotkey')
                break
        
        # Also check if it's in PATH
        if 'AutoHotkey' not in tools:
            try:
                result = subprocess.run(['where', 'AutoHotkey.exe'], capture_output=True, text=True, check=False)
                if result.returncode == 0:
                    tools.append('AutoHotkey')
            except:
                pass
    except:
        pass
    
    return list(set(tools))  # Remove duplicates

def detect_project_structure(project_path: Path) -> Dict[str, List[str]]:
    """Detect common project structure patterns"""
    structure = {
        'frontend_dirs': [],
        'backend_dirs': [],
        'config_dirs': []
    }
    
    # Common directory patterns
    frontend_patterns = ['src', 'frontend', 'client', 'web', 'ui', 'app']
    backend_patterns = ['backend', 'server', 'api', 'services']
    config_patterns = ['config', 'configs', 'settings']
    
    for item in project_path.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            dir_name = item.name.lower()
            
            if any(pattern in dir_name for pattern in frontend_patterns):
                structure['frontend_dirs'].append(item.name)
            elif any(pattern in dir_name for pattern in backend_patterns):
                structure['backend_dirs'].append(item.name)
            elif any(pattern in dir_name for pattern in config_patterns):
                structure['config_dirs'].append(item.name)
    
    return structure

def get_techstack_config_path(project_path: Path) -> Path:
    """Get the path to the techstack configuration file"""
    return project_path / ".VibeArch" / "VibeArch_Setup" / "techstack.yaml"

def get_techstack_ignore_path(project_path: Path) -> Path:
    """Get the path to the techstack ignore configuration file"""
    return project_path / ".VibeArch" / "VibeArch_Setup" / ".techstack-ignore.yaml"

def get_techstack_md_path(project_path: Path) -> Path:
    """Get the path to the techstack markdown file"""
    return project_path / ".VibeArch" / "TechStack" / "techstack.md"

def load_techstack_ignore_patterns(project_path: Path) -> List[str]:
    """Load techstack-specific ignore patterns from .techstack-ignore.yaml"""
    ignore_path = get_techstack_ignore_path(project_path)



    if ignore_path.exists():
        try:
            with open(ignore_path, 'r') as f:
                data = yaml.safe_load(f)
                if data and 'ignore' in data and isinstance(data['ignore'], list):
                    print(f"SUCCESS: Loaded techstack ignore patterns from {ignore_path}: {data['ignore']}")
                    return data['ignore']
                else:
                    print(f"ERROR: Invalid data structure in {ignore_path}: {data}")
        except Exception as e:
            print(f"ERROR: Exception loading techstack ignore file: {e}")

    # Default techstack ignore patterns - focused on techstack analysis
    print("FALLBACK: Using default techstack ignore patterns")
    return [
        '.git/',
        'node_modules/',
        '__pycache__/',
        '.venv/',
        'venv/',
        'env/',
        '.vibarch/',
        '.VibeArch/',
        'dist/',
        'build/',
        'out/',
        'target/',
        '.cache/',
        'coverage/',
        '.nyc_output/',
        'logs/',
        'tmp/',
        'temp/'
    ]

def should_ignore_for_techstack(path: Path, project_root: Path, ignore_patterns: List[str]) -> bool:
    """Check if a path should be ignored for techstack analysis"""
    try:
        rel_path = str(path.relative_to(project_root))
    except ValueError:
        return True

    # Replace backslashes with forward slashes for consistent pattern matching
    rel_path = rel_path.replace("\\", "/")

    # Get just the filename for wildcard matching
    filename = path.name

    # Debug for .ahk files specifically
    if filename.endswith('.ahk'):
        print(f"DEBUG: Checking .ahk file: {filename}")
        print(f"DEBUG: rel_path: {rel_path}")
        print(f"DEBUG: ignore_patterns: {ignore_patterns}")

    # Check against ignore patterns
    for pattern in ignore_patterns:
        if pattern.endswith('/'):
            # Directory pattern - check if path starts with this directory
            if rel_path.startswith(pattern) or rel_path == pattern.rstrip('/'):
                if filename.endswith('.ahk'):
                    print(f"DEBUG: .ahk file IGNORED by directory pattern: {pattern}")
                return True
            # Also check if any parent directory matches
            if ('/' + pattern.rstrip('/') + '/') in ('/' + rel_path + '/'):
                if filename.endswith('.ahk'):
                    print(f"DEBUG: .ahk file IGNORED by parent directory pattern: {pattern}")
                return True
        elif pattern.startswith('*.'):
            # Wildcard pattern - check file extension
            extension = pattern[1:]  # Remove the *
            if filename.endswith(extension):
                if filename.endswith('.ahk'):
                    print(f"DEBUG: .ahk file IGNORED by wildcard pattern: {pattern}")
                return True
        else:
            # Exact file pattern
            if rel_path == pattern or rel_path.endswith('/' + pattern):
                if filename.endswith('.ahk'):
                    print(f"DEBUG: .ahk file IGNORED by exact pattern: {pattern}")
                return True
            # Also check just the filename
            if filename == pattern:
                if filename.endswith('.ahk'):
                    print(f"DEBUG: .ahk file IGNORED by filename pattern: {pattern}")
                return True

    # If we get here and it's an .ahk file, it wasn't ignored
    if filename.endswith('.ahk'):
        print(f"DEBUG: .ahk file NOT IGNORED: {filename}")

    return False

def generate_techstack_markdown(project_path: Path, detected_data: Dict[str, Any]) -> str:
    """Generate markdown content for the techstack"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    project_name = project_path.name
    
    markdown_content = f"""# Technology Stack Analysis

**Last Updated:** {timestamp}  
**Project:** {project_name}  
**Project Path:** {project_path}

---

## 📋 Summary

This document provides a comprehensive analysis of the technology stack used in this project. The information below was automatically detected by scanning project files, dependencies, and configuration.

"""

    # Languages section
    if detected_data.get('languages'):
        markdown_content += "## 💻 Programming Languages\n\n"
        for lang in sorted(detected_data['languages']):
            markdown_content += f"- **{lang}**\n"
        markdown_content += "\n"

    # Frontend frameworks section
    if detected_data.get('frontend_frameworks'):
        markdown_content += "## 🎨 Frontend Frameworks & Libraries\n\n"
        for framework in sorted(detected_data['frontend_frameworks']):
            markdown_content += f"- **{framework}**\n"
        markdown_content += "\n"

    # Backend frameworks section
    if detected_data.get('backend_frameworks'):
        markdown_content += "## ⚙️ Backend Frameworks & Libraries\n\n"
        for framework in sorted(detected_data['backend_frameworks']):
            markdown_content += f"- **{framework}**\n"
        markdown_content += "\n"

    # Build tools section
    if detected_data.get('build_tools'):
        markdown_content += "## 🔧 Build Tools & Development\n\n"
        for tool in sorted(detected_data['build_tools']):
            markdown_content += f"- **{tool}**\n"
        markdown_content += "\n"

    # Automation tools section
    if detected_data.get('automation_tools'):
        markdown_content += "## 🤖 Automation & DevOps Tools\n\n"
        for tool in sorted(detected_data['automation_tools']):
            markdown_content += f"- **{tool}**\n"
        markdown_content += "\n"

    # Databases section
    if detected_data.get('databases'):
        markdown_content += "## 🗄️ Databases & Data Storage\n\n"
        for db in sorted(detected_data['databases']):
            markdown_content += f"- **{db}**\n"
        markdown_content += "\n"

    # Other technologies section
    if detected_data.get('other_technologies'):
        markdown_content += "## 🔌 Other Technologies\n\n"
        for tech in sorted(detected_data['other_technologies']):
            markdown_content += f"- **{tech}**\n"
        markdown_content += "\n"

    # Project structure section
    if detected_data.get('project_structure'):
        markdown_content += "## 📁 Project Structure Analysis\n\n"
        structure = detected_data['project_structure']
        
        if structure.get('frontend_dirs'):
            markdown_content += "### Frontend Directories\n"
            for dir_name in sorted(structure['frontend_dirs']):
                markdown_content += f"- `{dir_name}/`\n"
            markdown_content += "\n"
        
        if structure.get('backend_dirs'):
            markdown_content += "### Backend Directories\n"
            for dir_name in sorted(structure['backend_dirs']):
                markdown_content += f"- `{dir_name}/`\n"
            markdown_content += "\n"
        
        if structure.get('config_files'):
            markdown_content += "### Configuration Files\n"
            for file_name in sorted(structure['config_files']):
                markdown_content += f"- `{file_name}`\n"
            markdown_content += "\n"

    # Footer
    markdown_content += """---

## 📝 Notes

This analysis was automatically generated by Vibe Architect's techstack detection system. The information above reflects the current state of the project based on:

- **Package files**: `package.json`, `requirements.txt`, `Gemfile`, etc.
- **Configuration files**: Build tools, framework configs, environment files
- **Project structure**: Directory names and file extensions
- **Dependency analysis**: Direct and development dependencies

For manual customization of this techstack information, use the Vibe Architect interface or edit the `techstack.yaml` configuration file.

*Generated by Vibe Architect Techstack Analyzer*
"""

    return markdown_content

def save_techstack_markdown(project_path: Path, detected_data: Dict[str, Any]) -> str:
    """Save the techstack analysis as a markdown file"""
    md_path = get_techstack_md_path(project_path)
    
    # Create directory if it doesn't exist
    md_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Generate and save markdown content
    markdown_content = generate_techstack_markdown(project_path, detected_data)
    
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    return str(md_path)

def detect_databases(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect database technologies"""
    databases = []

    # Check requirements.txt for database drivers
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        if 'requirements.txt' in files:
            requirements_path = root_path / 'requirements.txt'
            if should_ignore_for_techstack(requirements_path, project_path, ignore_patterns):
                continue
            requirements_path = Path(root) / 'requirements.txt'
            try:
                with open(requirements_path, 'r') as f:
                    requirements = f.read().lower()
                
                if 'psycopg2' in requirements or 'postgresql' in requirements:
                    databases.append('PostgreSQL')
                if 'mysql' in requirements or 'pymysql' in requirements:
                    databases.append('MySQL')
                if 'sqlite' in requirements:
                    databases.append('SQLite')
                if 'redis' in requirements:
                    databases.append('Redis')
                if 'mongodb' in requirements or 'pymongo' in requirements:
                    databases.append('MongoDB')
                if 'elasticsearch' in requirements:
                    databases.append('Elasticsearch')
                if 'cassandra' in requirements:
                    databases.append('Cassandra')
                if 'influxdb' in requirements:
                    databases.append('InfluxDB')
                    
            except Exception as e:
                print(f"Error reading requirements.txt at {requirements_path}: {e}")
        
        # Check package.json for database drivers
        if 'package.json' in files:
            package_json_path = Path(root) / 'package.json'
            try:
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)
                
                dependencies = {}
                dependencies.update(package_data.get('dependencies', {}))
                dependencies.update(package_data.get('devDependencies', {}))
                
                if 'pg' in dependencies or 'postgres' in dependencies:
                    databases.append('PostgreSQL')
                if 'mysql' in dependencies or 'mysql2' in dependencies:
                    databases.append('MySQL')
                if 'sqlite3' in dependencies:
                    databases.append('SQLite')
                if 'redis' in dependencies:
                    databases.append('Redis')
                if 'mongodb' in dependencies or 'mongoose' in dependencies:
                    databases.append('MongoDB')
                    
            except Exception as e:
                print(f"Error reading package.json at {package_json_path}: {e}")
    
    # Check for database files
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        for file in files:
            file_path = root_path / file
            if should_ignore_for_techstack(file_path, project_path, ignore_patterns):
                continue
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                databases.append('SQLite')
    
    return list(set(databases))

def detect_other_technologies(project_path: Path, ignore_patterns: List[str] = None) -> List[str]:
    """Detect other technologies that don't fit standard categories"""
    other_tech = []
    if ignore_patterns is None:
        ignore_patterns = load_techstack_ignore_patterns(project_path)

    # Check for config files and other indicators
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        for file in files:
            file_path = root_path / file
            if should_ignore_for_techstack(file_path, project_path, ignore_patterns):
                continue
            file_lower = file.lower()
            
            # Configuration files
            if file_lower in ['makefile', 'cmake.txt', 'cmakecache.txt']:
                other_tech.append('Make/CMake')
            elif file_lower.endswith('.gradle'):
                other_tech.append('Gradle')
            elif file_lower.endswith('.maven') or file_lower == 'pom.xml':
                other_tech.append('Maven')
            elif file_lower in ['ansible.cfg', 'playbook.yml'] or file_lower.endswith('.ansible'):
                other_tech.append('Ansible')
            elif file_lower.endswith('.tf') or file_lower.endswith('.terraform'):
                other_tech.append('Terraform')
            elif file_lower.endswith('.k8s.yml') or file_lower.endswith('.k8s.yaml') or 'kubernetes' in file_lower:
                other_tech.append('Kubernetes')
            elif file_lower.endswith('.helm') or 'chart.yaml' in file_lower:
                other_tech.append('Helm')
            elif file_lower in ['vagrantfile']:
                other_tech.append('Vagrant')
            elif file_lower.endswith('.nginx') or file_lower == 'nginx.conf':
                other_tech.append('Nginx')
            elif file_lower.endswith('.apache') or file_lower == '.htaccess':
                other_tech.append('Apache')
            elif file_lower.endswith('.env') or file_lower == '.env.example':
                other_tech.append('Environment Config')
            elif file_lower.endswith('.toml'):
                other_tech.append('TOML Config')
            elif file_lower.endswith('.ini'):
                other_tech.append('INI Config')
            elif file_lower.endswith('.properties'):
                other_tech.append('Properties Config')
            elif file_lower.endswith('.xml') and 'config' in file_lower:
                other_tech.append('XML Config')
            elif file_lower.endswith('.proto'):
                other_tech.append('Protocol Buffers')
            elif file_lower.endswith('.graphql') or file_lower.endswith('.gql'):
                other_tech.append('GraphQL')
            elif file_lower.endswith('.md') or file_lower.endswith('.markdown'):
                other_tech.append('Markdown Documentation')
            elif file_lower.endswith('.rst'):
                other_tech.append('reStructuredText')
            elif file_lower.endswith('.tex'):
                other_tech.append('LaTeX')
            elif file_lower.endswith('.ipynb'):
                other_tech.append('Jupyter Notebooks')
            elif file_lower.endswith('.r') or file_lower.endswith('.rmd'):
                other_tech.append('R Markdown')
    
    # Check package.json for additional technologies
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        if 'package.json' in files:
            package_json_path = root_path / 'package.json'
            if should_ignore_for_techstack(package_json_path, project_path, ignore_patterns):
                continue
            package_json_path = Path(root) / 'package.json'
            try:
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)
                
                dependencies = {}
                dependencies.update(package_data.get('dependencies', {}))
                dependencies.update(package_data.get('devDependencies', {}))
                
                # Testing frameworks
                if 'jest' in dependencies:
                    other_tech.append('Jest Testing')
                if 'mocha' in dependencies:
                    other_tech.append('Mocha Testing')
                if 'cypress' in dependencies:
                    other_tech.append('Cypress Testing')
                if 'playwright' in dependencies:
                    other_tech.append('Playwright Testing')
                if 'puppeteer' in dependencies:
                    other_tech.append('Puppeteer')
                
                # State management
                if 'redux' in dependencies:
                    other_tech.append('Redux')
                if 'zustand' in dependencies:
                    other_tech.append('Zustand')
                if 'mobx' in dependencies:
                    other_tech.append('MobX')
                
                # Styling
                if 'tailwindcss' in dependencies:
                    other_tech.append('Tailwind CSS')
                if 'styled-components' in dependencies:
                    other_tech.append('Styled Components')
                if 'emotion' in dependencies:
                    other_tech.append('Emotion CSS')
                if 'sass' in dependencies or 'node-sass' in dependencies:
                    other_tech.append('Sass/SCSS')
                if 'less' in dependencies:
                    other_tech.append('Less CSS')
                
                # GraphQL
                if 'apollo' in dependencies or 'graphql' in dependencies:
                    other_tech.append('GraphQL/Apollo')
                
                # Electron
                if 'electron' in dependencies:
                    other_tech.append('Electron')
                
                # PWA
                if 'workbox' in dependencies or 'pwa' in dependencies:
                    other_tech.append('Progressive Web App')
                    
            except Exception as e:
                print(f"Error reading package.json at {package_json_path}: {e}")
    
    # Check requirements.txt for additional Python technologies
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)

        # Filter directories based on techstack ignore patterns
        dirs[:] = [d for d in dirs if not should_ignore_for_techstack(root_path / d, project_path, ignore_patterns)]

        if 'requirements.txt' in files:
            requirements_path = root_path / 'requirements.txt'
            if should_ignore_for_techstack(requirements_path, project_path, ignore_patterns):
                continue
            requirements_path = Path(root) / 'requirements.txt'
            try:
                with open(requirements_path, 'r') as f:
                    requirements = f.read().lower()
                
                # Testing frameworks
                if 'pytest' in requirements:
                    other_tech.append('Pytest Testing')
                if 'unittest' in requirements:
                    other_tech.append('Unittest')
                if 'nose' in requirements:
                    other_tech.append('Nose Testing')
                
                # Task queues
                if 'celery' in requirements:
                    other_tech.append('Celery')
                if 'rq' in requirements:
                    other_tech.append('Redis Queue')
                
                # Async frameworks
                if 'asyncio' in requirements:
                    other_tech.append('AsyncIO')
                if 'aiohttp' in requirements:
                    other_tech.append('AioHTTP')
                
                # Data processing
                if 'jupyter' in requirements:
                    other_tech.append('Jupyter')
                if 'matplotlib' in requirements:
                    other_tech.append('Matplotlib')
                if 'seaborn' in requirements:
                    other_tech.append('Seaborn')
                if 'plotly' in requirements:
                    other_tech.append('Plotly')
                    
            except Exception as e:
                print(f"Error reading requirements.txt at {requirements_path}: {e}")
    
    return list(set(other_tech))

@router.get("/detect")
async def detect_techstack(project_path: str = Query(..., description="Path to the project root")):
    """Detect the techstack of a project"""
    try:
        project_root = Path(project_path).resolve()

        if not project_root.exists():
            raise HTTPException(status_code=404, detail="Project path not found")
        
        # Load ignore patterns once for all detection functions
        ignore_patterns = load_techstack_ignore_patterns(project_root)

        # Detect various aspects of the techstack
        languages = detect_languages(project_root, ignore_patterns)
        frontend_frameworks = detect_frontend_frameworks(project_root, ignore_patterns)
        backend_frameworks = detect_backend_frameworks(project_root, ignore_patterns)
        build_tools = detect_build_tools(project_root, ignore_patterns)
        automation_tools = detect_automation_tools(project_root, ignore_patterns)
        databases = detect_databases(project_root, ignore_patterns)
        other_technologies = detect_other_technologies(project_root, ignore_patterns)
        project_structure = detect_project_structure(project_root)
        
        # Check if config file exists
        config_path = get_techstack_config_path(project_root)
        config_exists = config_path.exists()
        
        detected = {
            'languages': languages,
            'frontend_frameworks': frontend_frameworks,
            'backend_frameworks': backend_frameworks,
            'build_tools': build_tools,
            'automation_tools': automation_tools,
            'databases': databases,
            'other_technologies': other_technologies,
            'project_structure': project_structure
        }
        
        # Generate and save the techstack markdown file
        md_path = save_techstack_markdown(project_root, detected)
        
        return JSONResponse(content={
            'detected': detected,
            'config_exists': config_exists,
            'config_path': str(config_path) if config_exists else None,
            'markdown_path': md_path
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error detecting techstack: {str(e)}")

@router.get("/config")
async def get_techstack_config(project_path: str = Query(..., description="Path to the project root")):
    """Get the current techstack configuration"""
    try:
        project_root = Path(project_path).resolve()
        config_path = get_techstack_config_path(project_root)
        
        if not config_path.exists():
            raise HTTPException(status_code=404, detail="Techstack configuration not found")
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        return JSONResponse(content=config)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading techstack config: {str(e)}")

@router.post("/config")
async def save_techstack_config(request: TechstackSaveRequest):
    """Save techstack configuration"""
    try:
        project_root = Path(request.project_path).resolve()
        config_path = get_techstack_config_path(project_root)
        
        # Create directory if it doesn't exist
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save configuration
        with open(config_path, 'w') as f:
            yaml.dump(request.config, f, default_flow_style=False, sort_keys=False)
        
        return JSONResponse(content={
            'success': True,
            'message': 'Techstack configuration saved successfully',
            'config_path': str(config_path)
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving techstack config: {str(e)}")

@router.get("/ignore")
async def get_techstack_ignore_patterns(project_path: str = Query(..., description="Path to the project root")):
    """Get techstack ignore patterns"""
    try:
        project_root = Path(project_path).resolve()

        if not project_root.exists():
            raise HTTPException(status_code=404, detail="Project path not found")

        ignore_path = get_techstack_ignore_path(project_root)

        if ignore_path.exists():
            with open(ignore_path, 'r') as f:
                data = yaml.safe_load(f)
                patterns = data.get('ignore', []) if data else []
        else:
            # Return default patterns without calling load_techstack_ignore_patterns
            # to avoid potential recursion issues
            patterns = [
                '.git/',
                'node_modules/',
                '__pycache__/',
                '.venv/',
                'venv/',
                'env/',
                '.vibarch/',
                '.VibeArch/',
                'dist/',
                'build/',
                'out/',
                'target/',
                '.cache/',
                'coverage/',
                '.nyc_output/',
                'logs/',
                'tmp/',
                'temp/'
            ]

        return JSONResponse(content={
            'patterns': patterns,
            'config_exists': ignore_path.exists(),
            'config_path': str(ignore_path)
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading techstack ignore patterns: {str(e)}")

class TechstackIgnoreRequest(BaseModel):
    """Request model for saving techstack ignore patterns"""
    project_path: str
    patterns: List[str]

@router.post("/ignore")
async def save_techstack_ignore_patterns(request: TechstackIgnoreRequest):
    """Save techstack ignore patterns"""
    try:
        project_root = Path(request.project_path).resolve()

        if not project_root.exists():
            raise HTTPException(status_code=404, detail="Project path not found")

        ignore_path = get_techstack_ignore_path(project_root)

        # Create directory if it doesn't exist
        ignore_path.parent.mkdir(parents=True, exist_ok=True)

        # Save ignore patterns
        ignore_data = {'ignore': request.patterns}
        with open(ignore_path, 'w') as f:
            yaml.dump(ignore_data, f, default_flow_style=False, sort_keys=False)

        return JSONResponse(content={
            'success': True,
            'message': 'Techstack ignore patterns saved successfully',
            'config_path': str(ignore_path)
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving techstack ignore patterns: {str(e)}")