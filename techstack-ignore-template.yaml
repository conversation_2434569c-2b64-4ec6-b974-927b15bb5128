# Techstack Ignore Patterns
# This file defines patterns for files and directories to ignore during techstack analysis
# Each pattern should be on a new line and can use glob-style wildcards

# Version Control
.git/
.svn/
.hg/

# Dependencies and Virtual Environments
node_modules/
__pycache__/
.venv/
venv/
env/
.env/
vendor/
Packages/
packages/

# Build and Distribution
dist/
build/
out/
target/
bin/
obj/
.cache/
coverage/
.nyc_output/

# Logs and Temporary Files
logs/
tmp/
temp/
*.log
*.tmp

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# OS Files
.DS_Store
Thumbs.db
desktop.ini

# Vibearch Internal
.vibearch/
.VibeArch/

# Automation Tools (to exclude from techstack analysis)
*.ahk
*.ps1
*.bat
*.cmd
*.sh

# Specific file types that shouldn't be considered part of techstack
*.exe
*.dll
*.so
*.dylib
*.jar
*.war
*.ear
