import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../hooks/useProject';
import { useCodingAgent } from '../contexts/CodingAgentContext';
import api, { mvcdApi } from '../services/api';
import { API_URL } from '../services/api';
import yaml from 'js-yaml';

const MVCD = () => {
  const { currentProject } = useProject();
  const { codingAgent, setCodingAgent } = useCodingAgent();
  const [mvcdStatus, setMvcdStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tasks, setTasks] = useState({
    generate: null,
    enrich: null,
    analyze: null
  });
  const [activeTab, setActiveTab] = useState('overview');

  // New state variables for the code base view
  const [mvcdData, setMvcdData] = useState(null);
  const [mvcdLoading, setMvcdLoading] = useState(false);
  const [mvcdError, setMvcdError] = useState(null);
  const [directoryStructure, setDirectoryStructure] = useState(['/']);
  const [selectedDirectory, setSelectedDirectory] = useState('/');

  // New state variables for the ignore tab
  const [ignoreData, setIgnoreData] = useState([]);
  const [ignoreLoading, setIgnoreLoading] = useState(false);
  const [ignoreError, setIgnoreError] = useState(null);
  const [ignoreModified, setIgnoreModified] = useState(false);

  // New state variables for the directory tab
  const [projectDirectoryStructure, setProjectDirectoryStructure] = useState(null);
  const [directoryLoading, setDirectoryLoading] = useState(false);
  const [directoryError, setDirectoryError] = useState(null);
  const [expandedFolders, setExpandedFolders] = useState({});
  const [directoryStats, setDirectoryStats] = useState({ folders: 0, files: 0, totalLoc: 0 });

  // New state variables for the improvements tab
  const [improvementsData, setImprovementsData] = useState(null);
  const [improvementsLoading, setImprovementsLoading] = useState(false);
  const [improvementsError, setImprovementsError] = useState(null);
  const [selectedImprovements, setSelectedImprovements] = useState(new Set());
  const [executingImprovement, setExecutingImprovement] = useState(false);
  
  // New state variables for improvement file selection
  const [improvementFiles, setImprovementFiles] = useState([]);
  const [selectedImprovementFile, setSelectedImprovementFile] = useState(null);
  const [filesLoading, setFilesLoading] = useState(false);

  // New state variables for Step 1 refresh/recreate functionality
  const [showStep1Options, setShowStep1Options] = useState(false);
  const [showRecreateConfirm, setShowRecreateConfirm] = useState(false);

  // 1. PROJECT INITIALIZATION: Load MVCD status when component mounts or project changes
  // This effect handles the initial loading of project data and fallback to localStorage
  useEffect(() => {
    console.log('MVCD: useEffect triggered, currentProject:', currentProject);

    // Check if we have a current project
    if (currentProject?.path) {
      console.log('MVCD: Current project found in context:', currentProject.path, 'name:', currentProject.name);
      loadMvcdStatus();
    } else {
      console.log('MVCD: No current project in context, checking localStorage');
      // Try to load from localStorage as a fallback
      const savedProject = localStorage.getItem('currentProject');
      if (savedProject) {
        try {
          const projectData = JSON.parse(savedProject);
          console.log('MVCD: Found project in localStorage:', projectData.path, 'name:', projectData.name);
          // Use the project from localStorage for this component
          loadMvcdStatus(projectData);
        } catch (err) {
          console.error('MVCD: Error parsing saved project:', err);
          setLoading(false);
        }
      } else {
        console.log('MVCD: No project found in localStorage');
        setLoading(false);
      }
    }
  }, [currentProject]);

  // 2. UI EVENT HANDLERS: Close dropdown when clicking outside
  // This effect manages UI state for dropdown interactions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showStep1Options && !event.target.closest('.step1-dropdown')) {
        setShowStep1Options(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showStep1Options]);

  // 3. TASK MONITORING: Poll for task status updates and reload data when tasks complete
  // This effect manages background task polling and triggers data reloads when tasks finish
  useEffect(() => {
    // Only poll for tasks that are in a running state (not waiting for user)
    const taskIds = Object.values(tasks)
      .filter(task => task && task.status === 'running')
      .map(task => task.task_id);

    if (taskIds.length === 0) return;

    const interval = setInterval(async () => {
      // Get the active project for status updates
      const activeProject = getActiveProject();

      for (const taskId of taskIds) {
        try {
          console.log(`MVCD: Polling task status for task ${taskId}`);
          const response = await mvcdApi.getTaskStatus(taskId);
          const taskData = response.data;

          // Update the task status
          setTasks(prev => {
            const newTasks = { ...prev };

            // Find which task this is
            for (const [key, task] of Object.entries(prev)) {
              if (task && task.task_id === taskId) {
                newTasks[key] = taskData;

                // If task completed, failed, or waiting for user, reload MVCD status
                if (taskData.status === 'completed' || taskData.status === 'failed' || taskData.status === 'waiting_for_user') {
                  console.log(`MVCD: Task ${taskId} ${taskData.status}, reloading status`);
                  // Use the active project when reloading status
                  if (activeProject) {
                    loadMvcdStatus(activeProject);
                  }
                }

                break;
              }
            }

            return newTasks;
          });
        } catch (err) {
          console.error(`MVCD: Error polling task ${taskId}:`, err);
        }

      }
    }, 2000);

    return () => clearInterval(interval);
  }, [tasks]); // getActiveProject and loadMvcdStatus are defined in the component, so they don't need to be dependencies

  // 4. CODEBASE TAB DATA MANAGEMENT: Load and refresh MVCD data
  // These effects handle loading MVCD data when the codebase tab is selected and refreshing when data changes
  useEffect(() => {
    if (activeTab === 'codebase' && mvcdStatus?.mvcd_exists && !mvcdData) {
      fetchMvcdData();
    }
  }, [activeTab, mvcdStatus, mvcdData]);

  // Force refresh MVCD data when status changes (e.g., after regeneration)
  useEffect(() => {
    if (mvcdStatus?.mvcd_exists && mvcdData && mvcdStatus?.last_updated) {
      // Check if the MVCD file has been updated since we last loaded the data
      const lastLoadTime = mvcdData.loadedAt || 0;
      const fileLastUpdated = mvcdStatus.last_updated * 1000; // Convert to milliseconds
      
      if (fileLastUpdated > lastLoadTime) {
        console.log('MVCD file has been updated, refreshing data...');
        setMvcdData(null); // Clear cached data to trigger reload
      }
    }
  }, [mvcdStatus?.last_updated]);

  // 5. TAB-SPECIFIC DATA LOADING: Load data when different tabs are selected
  // These effects manage lazy loading of data for each tab to improve performance
  
  // Load ignore data when ignore tab is selected
  useEffect(() => {
    if (activeTab === 'ignore' && ignoreData.length === 0) {
      fetchIgnoreData();
    }
  }, [activeTab, ignoreData]);

  // Load directory data when overview tab is selected
  useEffect(() => {
    if (activeTab === 'overview' && !projectDirectoryStructure) {
      fetchDirectoryStructure();
    }
  }, [activeTab, projectDirectoryStructure]);

  // 6. IMPROVEMENTS TAB DATA MANAGEMENT: Handle improvement files and data loading
  // These effects manage the improvements tab file selection and data loading workflow
  
  // Load improvements data when improvements tab is selected
  useEffect(() => {
    if (activeTab === 'improvements') {
      if (improvementFiles.length === 0) {
        fetchImprovementFiles();
      } else if (!improvementsData && selectedImprovementFile) {
        fetchImprovementsData();
      }
    }
  }, [activeTab, improvementFiles, improvementsData, selectedImprovementFile]);

  // Load improvements when selected file changes
  useEffect(() => {
    if (selectedImprovementFile && activeTab === 'improvements') {
      fetchImprovementsData(selectedImprovementFile);
    }
  }, [selectedImprovementFile]);

  const loadMvcdStatus = async (projectOverride = null) => {
    // Use the provided project override or fall back to the current project
    const projectToUse = projectOverride || currentProject;

    if (!projectToUse?.path) {
      console.error('MVCD: Cannot load status - no project path available');
      setError('No project selected. Please select a project first.');
      setLoading(false);
      return;
    }

    // Validate that the project path exists
    try {
      // First check if the project directory exists by making a simple API call
      console.log('MVCD: Validating project path:', projectToUse.path);
      
      setLoading(true);
      setError(null);

      const response = await mvcdApi.getStatus(projectToUse.path);
      setMvcdStatus(response.data);
      console.log('MVCD: Status loaded successfully:', response.data);
    } catch (err) {
      console.error('MVCD: Error loading status:', err);
      
      // Check if it's a path-related error
      if (err.response?.status === 404 || err.message?.includes('not found') || err.message?.includes('does not exist')) {
        setError(`Project directory not found: ${projectToUse.path}. Please select a valid project.`);
        // Clear the invalid project from localStorage
        localStorage.removeItem('currentProject');
      } else if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        setError('Request timed out. The backend server might be slow or unresponsive.');
      } else if (err.request && !err.response) {
        setError('Cannot connect to the backend server. Please ensure the backend is running.');
      } else {
        setError(`Error loading MVCD status: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Get the current active project (either from context or localStorage)
  const getActiveProject = () => {
    console.log('MVCD: getActiveProject called, currentProject from context:', currentProject);

    if (currentProject?.path) {
      console.log('MVCD: Using project from context:', currentProject);
      return currentProject;
    }

    // Try to get from localStorage
    const savedProject = localStorage.getItem('currentProject');
    console.log('MVCD: Checking localStorage, savedProject:', savedProject);

    if (savedProject) {
      try {
        const parsed = JSON.parse(savedProject);
        console.log('MVCD: Using project from localStorage:', parsed);
        return parsed;
      } catch (err) {
        console.error('MVCD: Error parsing saved project:', err);
        return null;
      }
    }

    console.log('MVCD: No project found in context or localStorage');
    return null;
  };

  const handleGenerateMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Generating MVCD for project:', activeProject.path);
      const response = await mvcdApi.generate(activeProject.path);

      setTasks(prev => ({
        ...prev,
        generate: response.data
      }));
    } catch (err) {
      console.error('MVCD: Error generating MVCD:', err);
      setError(`Error generating MVCD: ${err.message || err}`);
    }
  };

  const handleRefreshMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Refreshing MVCD for project (adding new files only):', activeProject.path);
      const response = await mvcdApi.generate(activeProject.path, {
        refresh_only: true
      });

      setTasks(prev => ({
        ...prev,
        generate: response.data
      }));
      setShowStep1Options(false);
    } catch (err) {
      console.error('MVCD: Error refreshing MVCD:', err);
      setError(`Error refreshing MVCD: ${err.message || err}`);
    }
  };

  const handleRecreateMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Recreating MVCD for project (complete rebuild):', activeProject.path);
      const response = await mvcdApi.generate(activeProject.path, {
        force_recreate: true
      });

      setTasks(prev => ({
        ...prev,
        generate: response.data
      }));
      setShowRecreateConfirm(false);
      setShowStep1Options(false);
    } catch (err) {
      console.error('MVCD: Error recreating MVCD:', err);
      setError(`Error recreating MVCD: ${err.message || err}`);
    }
  };

  const handleEnrichMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Enriching MVCD for project:', activeProject.path, 'with agent:', codingAgent);
      const response = await mvcdApi.enrich(activeProject.path, {
        coding_agent_type: codingAgent, // Use the selected coding agent
        headless: false,
        timeout: 300
      });

      setTasks(prev => ({
        ...prev,
        enrich: response.data
      }));
    } catch (err) {
      console.error('MVCD: Error enriching MVCD:', err);
      setError(`Error enriching MVCD: ${err.message || err}`);
    }
  };

  const handleAnalyzeMvcd = async () => {
    const activeProject = getActiveProject();
    if (!activeProject) {
      setError('No project selected');
      return;
    }

    try {
      const response = await mvcdApi.analyze(activeProject.path);
      setTasks(prev => ({ ...prev, analyze: response.data }));
    } catch (err) {
      console.error('Error starting MVCD analysis:', err);
      setError(err.response?.data?.detail || 'Failed to start analysis');
    }
  };

  const handleTestWindowSelection = async () => {
    try {
      console.log('Testing AutoHotkey window selection directly...');
      
      // Create a simple test script content
      const testScript = `
; Test AutoHotkey Window Selection for ${codingAgent}
; This script attempts to find and activate the ${codingAgent} window

${codingAgent === 'cursor' ? 'WinActivate, ahk_exe Cursor.exe' : 
  codingAgent === 'augment' ? 'WinActivate, ahk_class Chrome_WidgetWin_1' :
  'WinActivate, ahk_class Chrome_WidgetWin_1'}

${codingAgent === 'cursor' ? 'if WinExist("ahk_exe Cursor.exe")' :
  codingAgent === 'augment' ? 'if WinExist("ahk_class Chrome_WidgetWin_1")' :
  'if WinExist("ahk_class Chrome_WidgetWin_1")'}
{
    WinActivate
    Sleep, 500
    ; Show a message to confirm the window was found and activated
    ToolTip, ${codingAgent} window found and activated successfully!, 10, 10
    Sleep, 2000
    ToolTip
}
else
{
    ; Show error message if window not found
    MsgBox, 4112, Test Result, ${codingAgent} window not found. Make sure ${codingAgent} is running.
}
`;

      // Create a blob and download it as a .ahk file for the user to run manually
      const blob = new Blob([testScript], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `test_${codingAgent}_window_selection.ahk`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      setError(null);
      console.log(`AutoHotkey test script for ${codingAgent} downloaded successfully`);
      
      // Show success message
      alert(`Test script downloaded as 'test_${codingAgent}_window_selection.ahk'.\n\nDouble-click the downloaded file to test window selection for ${codingAgent}.`);
      
    } catch (err) {
      console.error('Error creating test script:', err);
      setError('Failed to create test script: ' + err.message);
    }
  };

  // Render status indicator
  const renderStatusIndicator = (status, overrideStatus = null) => {
    // If there's an override status, use that instead
    const effectiveStatus = overrideStatus || status;

    if (effectiveStatus === 'running') {
      return <div className="h-3 w-3 rounded-full bg-yellow-500"></div>;
    } else if (effectiveStatus === 'waiting_for_user') {
      return <div className="h-3 w-3 rounded-full bg-blue-500 animate-pulse"></div>;
    } else if (effectiveStatus === 'completed') {
      return <div className="h-3 w-3 rounded-full bg-green-500"></div>;
    } else if (effectiveStatus === 'failed') {
      return <div className="h-3 w-3 rounded-full bg-red-500"></div>;
    } else {
      return <div className="h-3 w-3 rounded-full bg-gray-300"></div>;
    }
  };

  // Format date
  const formatDate = (timestamp) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp * 1000);
    // Format as YYYY-MM-DD
    return date.toISOString().split('T')[0];
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${Math.round(value)}%`;
  };

  // Handle coding agent selection change
  const handleCodingAgentChange = (e) => {
    const newAgent = e.target.value;
    setCodingAgent(newAgent);
    // Save preference to localStorage
    localStorage.setItem('preferredCodingAgent', newAgent);
  };

  // Get the active project name for display
  const getActiveProjectName = () => {
    const activeProject = getActiveProject();
    console.log('MVCD: getActiveProjectName called, activeProject:', activeProject);

    if (activeProject?.name) {
      console.log('MVCD: Using project name:', activeProject.name);
      return activeProject.name;
    }
    if (activeProject?.path) {
      // Extract name from path if no name is available
      const pathName = activeProject.path.split(/[/\\]/).pop();
      console.log('MVCD: Using path-derived name:', pathName);
      return pathName;
    }
    console.log('MVCD: No project name available');
    return null;
  };

  // Handle project selection if no project is loaded
  const handleProjectSelection = () => {
    // Navigate to projects page
    window.location.href = '/projects';
  };

  // Fetch MVCD data from the server
  const fetchMvcdData = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setMvcdError('No project selected. Please select a project first.');
      return;
    }

    try {
      setMvcdLoading(true);
      setMvcdError(null);

      // Get the MVCD file path
      const mvcdPath = `${activeProject.path}/.VibeArch/Directory/mvcd.yaml`;
      console.log('Fetching MVCD data from:', mvcdPath);

      // Fetch the MVCD file content
      const response = await mvcdApi.getContent(activeProject.path);

      if (response.data && response.data.content) {
        // Parse YAML content
        const parsedData = yaml.load(response.data.content);
        // Add timestamp when data was loaded
        parsedData.loadedAt = Date.now();
        setMvcdData(parsedData);
        console.log('MVCD data loaded successfully. Total items:', parsedData?.codebase?.length || 0);
        console.log('Backend items:', parsedData?.codebase?.filter(item => item.file?.startsWith('backend/'))?.length || 0);
        console.log('Frontend items:', parsedData?.codebase?.filter(item => item.file?.startsWith('frontend-vite/'))?.length || 0);
        console.log('Root level items:', parsedData?.codebase?.filter(item => !item.file?.includes('/'))?.length || 0);
        console.log('Current selectedDirectory:', selectedDirectory);

        // Extract directory structure from the MVCD data
        parseMvcdDataToDirectoryStructure(parsedData);
      } else {
        throw new Error('Invalid MVCD data format');
      }
    } catch (err) {
      console.error('Error fetching MVCD data:', err);
      setMvcdError(`Error fetching MVCD data: ${err.message || err}`);
    } finally {
      setMvcdLoading(false);
    }
  };

  // Manual refresh function to clear cache and reload data
  const handleRefreshMvcdData = () => {
    console.log('Manually refreshing MVCD data...');
    setMvcdData(null); // Clear cached data
    // The useEffect will automatically trigger fetchMvcdData when mvcdData becomes null
  };

  // Fetch ignore data from the server
  const fetchIgnoreData = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setIgnoreError('No project selected. Please select a project first.');
      return;
    }

    try {
      setIgnoreLoading(true);
      setIgnoreError(null);

      console.log('=== FETCHIGNOREDATA DEBUG START ===');
      console.log('Active project path:', activeProject.path);
      console.log('Expected ignore file location:', `${activeProject.path}/.VibeArch/VibeArch_Setup/.mvcd-ignore.yaml`);

      // Fetch the ignore file content
      const response = await mvcdApi.getIgnore(activeProject.path);
      
      console.log('API Response status:', response.status);
      console.log('API Response data:', response.data);

      if (response.data && response.data.content) {
        // Parse the YAML content and convert to array of objects
        const yamlContent = response.data.content;
        console.log('Raw YAML content received:', yamlContent);
        console.log('YAML content length:', yamlContent.length);
        console.log('YAML content (first 500 chars):', yamlContent.substring(0, 500));
        
        try {
          const parsedYaml = yaml.load(yamlContent);
          console.log('Parsed YAML:', parsedYaml);
          
          const ignorePatterns = parsedYaml?.ignore || [];
          console.log('Extracted ignore patterns:', ignorePatterns);
          console.log('Number of patterns found:', ignorePatterns.length);
          
          // Convert to array of objects with id, pattern, and enabled fields
          const ignoreObjects = ignorePatterns.map((pattern, index) => ({
            id: index,
            pattern: pattern,
            enabled: true
          }));
          
          setIgnoreData(ignoreObjects);
          console.log('Ignore data loaded successfully:', ignoreObjects);
          console.log('=== FETCHIGNOREDATA DEBUG END ===');
        } catch (yamlError) {
          console.error('Error parsing YAML:', yamlError);
          console.log('Raw YAML that failed to parse:', yamlContent);
          setIgnoreError(`Error parsing ignore file: ${yamlError.message}`);
          setIgnoreData([]);
        }
      } else {
        console.log('No content in response, using default patterns');
        console.log('Response structure:', JSON.stringify(response.data, null, 2));
        // If no ignore file exists, create a default one
        const defaultPatterns = [
          ".git/",
          ".svn/",
          "node_modules/",
          ".venv/",
          "venv/",
          "env/",
          "dist/",
          "build/",
          "out/",
          "target/",
          ".cache/",
          "__pycache__/",
          "*.pyc",
          ".vscode/",
          ".idea/",
          "*.swp",
          "*.swo",
          ".DS_Store",
          "Thumbs.db",
          "*.log",
          "logs/",
          "tmp/",
          "temp/",
          "*.tmp",
          "coverage/",
          ".nyc_output/",
          "package-lock.json",
          "yarn.lock",
          "Pipfile.lock",
          ".env",
          ".env.local",
          ".env.*.local"
        ];
        
        const defaultIgnoreObjects = defaultPatterns.map((pattern, index) => ({
          id: index,
          pattern: pattern,
          enabled: true
        }));
        
        setIgnoreData(defaultIgnoreObjects);
      }
    } catch (err) {
      console.error('Error fetching ignore data:', err);
      if (err.response?.status === 404) {
        // If ignore file doesn't exist, create a default one
        const defaultPatterns = [
          ".git/",
          ".svn/",
          "node_modules/",
          ".venv/",
          "venv/",
          "env/",
          "dist/",
          "build/",
          "out/",
          "target/",
          ".cache/",
          "__pycache__/",
          "*.pyc",
          ".vscode/",
          ".idea/",
          "*.swp",
          "*.swo",
          ".DS_Store",
          "Thumbs.db",
          "*.log",
          "logs/",
          "tmp/",
          "temp/",
          "*.tmp",
          "coverage/",
          ".nyc_output/",
          "package-lock.json",
          "yarn.lock",
          "Pipfile.lock",
          ".env",
          ".env.local",
          ".env.*.local"
        ];
        
        const defaultIgnoreObjects = defaultPatterns.map((pattern, index) => ({
          id: index,
          pattern: pattern,
          enabled: true
        }));
        
        setIgnoreData(defaultIgnoreObjects);
      } else {
        setIgnoreError(`Error loading ignore data: ${err.message || err}`);
        setIgnoreData([]);
      }
    } finally {
      setIgnoreLoading(false);
    }
  };

  // Save ignore data to the server
  const saveIgnoreData = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setIgnoreError('No project selected. Please select a project first.');
      return;
    }

    try {
      setIgnoreLoading(true);
      setIgnoreError(null);

      // Convert ignoreData array back to YAML format
      const enabledPatterns = ignoreData.filter(item => item.enabled).map(item => item.pattern);
      const yamlContent = `# MVCD Ignore Patterns
# This file specifies which files and directories should be ignored during MVCD scanning
# Use glob patterns to match files and directories

ignore:
${enabledPatterns.map(pattern => `  - "${pattern}"`).join('\n')}
`;

      // Save the ignore file content
      const response = await mvcdApi.saveIgnore(activeProject.path, yamlContent);

      if (response.data && response.data.success) {
        console.log('Ignore data saved successfully');
        setIgnoreModified(false);
      } else {
        throw new Error('Failed to save ignore data');
      }
    } catch (err) {
      console.error('Error saving ignore data:', err);
      setIgnoreError(`Error saving ignore data: ${err.message || err}`);
    } finally {
      setIgnoreLoading(false);
    }
  };

  // Handle toggle of ignore patterns
  const handleIgnoreToggle = (id) => {
    setIgnoreData(prevData => 
      prevData.map(item => 
        item.id === id ? { ...item, enabled: !item.enabled } : item
      )
    );
    setIgnoreModified(true);
  };

  // Reload ignore data from server
  const handleReloadIgnoreData = () => {
    console.log('Manually reloading ignore data...');
    console.log('Current ignore data length:', ignoreData.length);
    
    // Reset the modified state since we're reloading
    setIgnoreModified(false);
    
    // Force reload by calling fetchIgnoreData directly
    fetchIgnoreData();
  };

  // Hard reload - clear all state and reload everything
  const handleHardReloadIgnoreData = () => {
    console.log('Hard reloading ignore data - clearing all state...');
    
    // Clear all ignore-related state
    setIgnoreData([]);
    setIgnoreError(null);
    setIgnoreModified(false);
    setIgnoreLoading(false);
    
    // Force a small delay to ensure state is cleared
    setTimeout(() => {
      fetchIgnoreData();
    }, 100);
  };

  // Fetch improvement files from the server
  const fetchImprovementFiles = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setImprovementsError('No project selected. Please select a project first.');
      return;
    }

    try {
      setFilesLoading(true);
      setImprovementsError(null);

      const response = await mvcdApi.getImprovementFiles(activeProject.path);

      if (response.data && response.data.files) {
        setImprovementFiles(response.data.files);
        console.log('Improvement files loaded successfully:', response.data.files);
        
        // Auto-select the first (newest) file if available and no file is currently selected
        if (response.data.files.length > 0 && !selectedImprovementFile) {
          setSelectedImprovementFile(response.data.files[0].name);
        }
      } else {
        setImprovementFiles([]);
      }
    } catch (err) {
      console.error('Error fetching improvement files:', err);
      setImprovementsError(`Error loading improvement files: ${err.message || err}`);
      setImprovementFiles([]);
    } finally {
      setFilesLoading(false);
    }
  };

  // Fetch improvements data from the server
  const fetchImprovementsData = async (fileName = null) => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setImprovementsError('No project selected. Please select a project first.');
      return;
    }

    try {
      setImprovementsLoading(true);
      setImprovementsError(null);

      // Use selected file if no specific file is provided
      const fileToLoad = fileName || selectedImprovementFile;
      
      // Fetch the improvements data
      const response = await mvcdApi.getImprovements(activeProject.path, fileToLoad);

      if (response.data && response.data.improvements) {
        setImprovementsData(response.data.improvements);
        console.log('Improvements data loaded successfully:', response.data.improvements);
      } else {
        setImprovementsData([]);
      }
    } catch (err) {
      console.error('Error fetching improvements data:', err);
      setImprovementsError(`Error loading improvements: ${err.message || err}`);
      setImprovementsData([]);
    } finally {
      setImprovementsLoading(false);
    }
  };

  // Update improvement priority
  const updateImprovementPriority = async (improvementId, priority) => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setImprovementsError('No project selected. Please select a project first.');
      return;
    }

    try {
      const response = await mvcdApi.updateImprovement(activeProject.path, improvementId, priority, null);

      if (response.data && response.data.success) {
        console.log('Improvement priority updated successfully');
        // Refresh the improvements data
        fetchImprovementsData();
      } else {
        throw new Error('Failed to update improvement priority');
      }
    } catch (err) {
      console.error('Error updating improvement priority:', err);
      setImprovementsError(`Error updating improvement: ${err.message || err}`);
    }
  };

  // Mark improvement as done
  const markImprovementDone = async (improvementId) => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setImprovementsError('No project selected. Please select a project first.');
      return;
    }

    try {
      const doneTimestamp = Date.now();
      const response = await mvcdApi.updateImprovement(activeProject.path, improvementId, null, doneTimestamp);

      if (response.data && response.data.success) {
        console.log('Improvement marked as done successfully');
        // Refresh the improvements data
        fetchImprovementsData();
      } else {
        throw new Error('Failed to mark improvement as done');
      }
    } catch (err) {
      console.error('Error marking improvement as done:', err);
      setImprovementsError(`Error updating improvement: ${err.message || err}`);
    }
  };

  // Execute selected improvements
  const executeSelectedImprovements = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setImprovementsError('No project selected. Please select a project first.');
      return;
    }

    if (selectedImprovements.size === 0) {
      setImprovementsError('No improvements selected for execution.');
      return;
    }

    try {
      setExecutingImprovement(true);
      setImprovementsError(null);

      // Get the selected improvements
      const selectedItems = improvementsData.filter(item => selectedImprovements.has(item.id));

      // Create a combined prompt for all selected improvements
      const improvementsList = selectedItems.map((improvement, index) => 
        `${index + 1}. **${improvement.title}** (Priority: ${improvement.priority || 'Medium'})
   - Description: ${improvement.description}
   - File: \`${improvement.file}\`
   ${improvement.element ? `- Element: \`${improvement.element}\`` : ''}
   ${improvement.line ? `- Line: ${improvement.line}` : ''}
   - Reasoning: ${improvement.reasoning}
`).join('\n');

      const promptContent = `# Execute Selected Code Improvements

You are tasked with implementing the following code improvements in the project. Please analyze each improvement and implement the necessary changes.

## Selected Improvements:

${improvementsList}

## Instructions:

1. **Analyze each improvement** to understand what changes are needed
2. **Implement the changes** in the appropriate files
3. **Ensure code quality** and maintain existing functionality
4. **Test your changes** to verify they work correctly
5. **Document any significant changes** you make

## Guidelines:

- Follow the existing code style and patterns
- Maintain backward compatibility where possible
- Add appropriate comments for complex changes
- Consider the impact on other parts of the codebase
- If you encounter conflicts or issues, explain them clearly

## Files to Modify:
${selectedItems.map(improvement => `- \`${improvement.file}\``).join('\n')}

Please implement these improvements systematically and provide a summary of the changes made.`;

      // Update the execute_selected_improvement.yaml file
      const response = await mvcdApi.executeImprovement(activeProject.path, promptContent, selectedItems[0]);

      if (response.data && response.data.success) {
        console.log('Execute improvement prompt updated successfully');

        // Now trigger the automation similar to Step 2 enrichment
        const enrichResponse = await mvcdApi.enrich(activeProject.path, {
          coding_agent_type: codingAgent,
          headless: false,
          timeout: 300,
          prompt_type: 'execute_improvement' // Special flag to use the execute improvement prompt
        });

        if (enrichResponse.data) {
          console.log('Improvement execution automation started');
          // Clear the selection after successful execution
          setSelectedImprovements(new Set());
        }
      } else {
        throw new Error('Failed to update execute improvement prompt');
      }
    } catch (err) {
      console.error('Error executing selected improvements:', err);
      setImprovementsError(`Error executing improvements: ${err.message || err}`);
    } finally {
      setExecutingImprovement(false);
    }
  };

  // Helper functions for improvement management
  const handlePriorityChange = (improvementId, newPriority) => {
    if (newPriority === 'done') {
      markImprovementDone(improvementId);
    } else {
      updateImprovementPriority(improvementId, newPriority);
    }
  };

  const handleImprovementSelection = (improvementId, isSelected) => {
    setSelectedImprovements(prev => {
      const newSet = new Set(prev);
      if (isSelected) {
        newSet.add(improvementId);
      } else {
        newSet.delete(improvementId);
      }
      return newSet;
    });
  };

  const handleSelectAllImprovements = (isSelected) => {
    if (isSelected && improvementsData) {
      setSelectedImprovements(new Set(improvementsData.map(item => item.id)));
    } else {
      setSelectedImprovements(new Set());
    }
  };

  const handleImprovementFileChange = (fileName) => {
    setSelectedImprovementFile(fileName);
    setSelectedImprovements(new Set()); // Clear selected improvements when changing files
    fetchImprovementsData(fileName);
  };

  // Helper function for ignore data management
  const handleIgnoreDataChange = (newData) => {
    setIgnoreData(newData);
    setIgnoreModified(true);
  };

  // Directory functions
  const fetchDirectoryStructure = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setDirectoryError('No project selected. Please select a project first.');
      return;
    }

    try {
      setDirectoryLoading(true);
      setDirectoryError(null);

      // Use the existing directory API to get directory structure
      const response = await api.get(`/directory/list?path=${encodeURIComponent(activeProject.path)}`);
      
      if (!response.data || !response.data.directories) {
        throw new Error('Invalid response format from directory API');
      }

      // Build a simple directory tree from the response
      const buildDirectoryTree = (entries, basePath) => {
        const pathParts = basePath.split(/[/\\]/);
        const name = pathParts[pathParts.length - 1] || basePath;

        const node = {
          name: name,
          path: basePath,
          type: 'folder',
          children: []
        };

        // Add direct children from the API response
        entries.forEach(entry => {
          if (entry.isDirectory) {
            node.children.push({
              name: entry.name,
              path: entry.path,
              type: 'folder',
              children: [] // We'll load children on demand when folders are expanded
            });
          } else {
            node.children.push({
              name: entry.name,
              path: entry.path,
              type: 'file'
            });
          }
        });

        return node;
      };

      const directoryTree = buildDirectoryTree(response.data.directories, activeProject.path);
      setProjectDirectoryStructure(directoryTree);
      
      // Calculate stats from the directory structure
      const stats = calculateDirectoryStats(directoryTree);
      setDirectoryStats(stats);

    } catch (error) {
      console.error('Error fetching directory structure:', error);
      setDirectoryError(`Failed to load directory structure: ${error.message}`);
    } finally {
      setDirectoryLoading(false);
    }
  };

  const toggleFolder = (path) => {
    setExpandedFolders(prev => ({
      ...prev,
      [path]: !prev[path]
    }));
  };

  const calculateDirectoryStats = (node) => {
    let folders = 0;
    let files = 0;
    let totalLoc = 0;

    const traverse = (currentNode) => {
      if (currentNode.type === 'folder') {
        folders++;
        if (currentNode.children) {
          currentNode.children.forEach(child => traverse(child));
        }
      } else if (currentNode.type === 'file') {
        files++;

        // For directory structure, we don't have LOC data directly
        // LOC will be calculated separately from MVCD data
        // This is just for counting files and folders
      }
    };

    traverse(node);
    return { folders, files, totalLoc };
  };

  const renderDirectoryNode = (node, level = 0) => {
    const isExpanded = expandedFolders[node.path];

    if (node.type === 'folder') {
      return (
        <div key={node.path} style={{ marginLeft: `${level * 20}px` }}>
          <div
            className="flex items-center py-1 cursor-pointer hover:bg-gray-100"
            onClick={() => toggleFolder(node.path)}
          >
            <span className="mr-2">
              {isExpanded ? (
                <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              ) : (
                <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
            </span>
            <svg className="h-5 w-5 text-blue-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
            </svg>
            <span>{node.name}</span>
          </div>
          {isExpanded && node.children && node.children.map(child => renderDirectoryNode(child, level + 1))}
        </div>
      );
    } else {
      return (
        <div key={node.path} style={{ marginLeft: `${level * 20}px` }} className="flex items-center py-1">
          <span className="mr-2 w-4"></span>
          <svg className="h-5 w-5 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>{node.name}</span>
        </div>
      );
    }
  };

  // Parse MVCD data and organize files by directory structure for UI display
  // This function extracts directory paths from MVCD file entries and creates a hierarchical structure
  const parseMvcdDataToDirectoryStructure = (data) => {
    if (!data || !data.codebase || !Array.isArray(data.codebase)) {
      setDirectoryStructure(['/']); // Always include root directory
      return;
    }

    // Get all unique directories from file paths
    const directories = new Set();
    directories.add('/'); // Add root directory

    data.codebase.forEach(item => {
      if (item.file) {
        // Split the file path into directories
        const parts = item.file.split('/');
        let currentPath = '';

        // Add each directory level
        for (let i = 0; i < parts.length - 1; i++) {
          currentPath += (currentPath ? '/' : '') + parts[i];
          directories.add(currentPath);
        }
      }
    });

    // Convert to array and sort
    const sortedDirs = Array.from(directories).sort((a, b) => {
      // Count slashes to determine depth
      const depthA = (a.match(/\//g) || []).length;
      const depthB = (b.match(/\//g) || []).length;

      // Sort by depth first, then alphabetically
      if (depthA !== depthB) return depthA - depthB;
      return a.localeCompare(b);
    });

    setDirectoryStructure(sortedDirs);
  };

  // Handle directory selection change
  const handleDirectoryChange = (e) => {
    setSelectedDirectory(e.target.value);
  };

  // Get indentation level for a file path
  const getIndentationLevel = (filePath) => {
    if (!filePath) return 0;

    // Count the number of directory separators
    return (filePath.match(/\//g) || []).length;
  };

  // Get color based on directory depth
  const getDirectoryColor = (filePath) => {
    if (!filePath) return 'text-gray-900';

    const depth = (filePath.match(/\//g) || []).length;

    // Color scale from darkest to lightest based on depth
    const colors = [
      'text-gray-900', // Root (darkest)
      'text-gray-800',
      'text-gray-700',
      'text-gray-600',
      'text-gray-500' // Deepest (lightest)
    ];

    // Cap at the maximum available color
    const colorIndex = Math.min(depth, colors.length - 1);
    return colors[colorIndex];
  };

  // Get background color based on directory depth
  const getDirectoryBgColor = (filePath) => {
    if (!filePath) return '';

    const depth = (filePath.match(/\//g) || []).length;

    // Background colors based on depth (very subtle)
    const bgColors = [
      '', // Root (no special bg)
      'bg-blue-50',
      'bg-indigo-50',
      'bg-purple-50',
      'bg-pink-50'
    ];

    // Only apply background color for deeper levels
    if (depth === 0) return '';

    // Cap at the maximum available color
    const colorIndex = Math.min(depth, bgColors.length - 1);
    return bgColors[colorIndex];
  };

  // Check if a file is in the selected directory
  const isInSelectedDirectory = (filePath) => {
    if (selectedDirectory === '/') return true; // Root shows all

    // Check if the file path starts with the selected directory
    return filePath.startsWith(selectedDirectory + '/');
  };

  return (
    <div className="p-3 w-full h-full flex flex-col">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-xl font-bold">Minimum Viable Code Description (MVCD)</h1>
        {getActiveProjectName() && (
          <div className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
            Project: {getActiveProjectName()}
          </div>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">Loading MVCD status...</div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex flex-col">
            <div className="text-red-700 mb-2">{error}</div>
            {(error.includes('No project selected') || error.includes('Project directory not found')) && (
              <button
                onClick={handleProjectSelection}
                className="self-start inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go to Projects
              </button>
            )}
          </div>
        </div>
      ) : !getActiveProject() ? (
        <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
          <div className="flex flex-col">
            <div className="text-yellow-700 mb-2">No project selected. Please select a project first.</div>
            <button
              onClick={handleProjectSelection}
              className="self-start inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Go to Projects
            </button>
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col">
          {/* MVCD Workflow - Moved up from the workflow tab */}
          <div className="bg-gray-50 shadow sm:rounded-lg mb-3 border border-gray-300">
            <div className="px-3 py-2">
              <h3 className="text-base font-medium text-gray-900">MVCD Workflow</h3>
              <p className="mt-1 max-w-2xl text-xs text-gray-500">
                Three steps to build a comprehensive understanding of your codebase.
              </p>

              <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
                {/* Step 1: Generate MVCD */}
                <div className={`p-2 rounded-lg border relative ${mvcdStatus?.mvcd_exists ? 'bg-green-50 border-green-300' : 'bg-gray-100 border-gray-300'}`}>
                  <div className="flex items-center">
                    <div className="mr-2">
                      {/* Show green status if MVCD exists, otherwise show task status */}
                      {renderStatusIndicator(tasks.generate?.status, mvcdStatus?.mvcd_exists ? 'completed' : null)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 1: Analyze Codebase</h4>
                      <p className="text-xs text-gray-500">
                        Scans files, elements, dependencies, and LOC.
                      </p>
                    </div>
                    <div className="relative">
                      {!mvcdStatus?.mvcd_exists ? (
                        <button
                          onClick={handleGenerateMvcd}
                          disabled={tasks.generate?.status === 'running'}
                          className={`px-2 py-1 rounded text-xs font-medium text-white ${
                            tasks.generate?.status === 'running'
                              ? 'bg-gray-400 cursor-not-allowed'
                              : 'bg-blue-600 hover:bg-blue-700'
                          }`}
                        >
                          {tasks.generate?.status === 'running' ? 'Running...' : 'Start'}
                        </button>
                      ) : (
                        <div className="relative step1-dropdown">
                          <button
                            onClick={() => setShowStep1Options(!showStep1Options)}
                            disabled={tasks.generate?.status === 'running'}
                            className={`px-2 py-1 rounded text-xs font-medium text-white flex items-center ${
                              tasks.generate?.status === 'running'
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-green-600 hover:bg-green-700'
                            }`}
                          >
                            {tasks.generate?.status === 'running' ? 'Running...' : 'Update'}
                            <svg className="ml-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                          
                          {showStep1Options && (
                            <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-300 rounded-md shadow-xl z-[9999]">
                              <button
                                onClick={handleRefreshMvcd}
                                className="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 rounded-t-md"
                              >
                                Refresh
                                <div className="text-xs text-gray-500">Add new files only</div>
                              </button>
                              <button
                                onClick={() => {
                                  setShowStep1Options(false);
                                  setShowRecreateConfirm(true);
                                }}
                                className="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 border-t border-gray-200 rounded-b-md"
                              >
                                Recreate
                                <div className="text-xs text-gray-500">Complete rebuild</div>
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Step 2: Enrich MVCD */}
                <div className="bg-gray-100 p-2 rounded-lg border border-gray-300">
                  <div className="flex items-center">
                    <div className="mr-2">
                      {renderStatusIndicator(tasks.enrich?.status)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 2: Enrich MVCD</h4>
                      <p className="text-xs text-gray-500">
                        Uses {codingAgent} to generate descriptions.
                      </p>
                    </div>
                    <div className="flex space-x-1">
                      <button
                        onClick={handleTestWindowSelection}
                        disabled={tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700'
                        }`}
                        title="Test AutoHotkey window selection"
                      >
                        Test
                      </button>
                      <button
                        onClick={handleEnrichMvcd}
                        disabled={!mvcdStatus?.mvcd_exists || tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          !mvcdStatus?.mvcd_exists || tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {tasks.enrich?.status === 'running'
                          ? 'Running...'
                          : tasks.enrich?.status === 'waiting_for_user'
                            ? 'Wait'
                            : 'Start'}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Step 3: Analyze MVCD */}
                <div className="bg-gray-100 p-2 rounded-lg border border-gray-300">
                  <div className="flex items-center">
                    <div className="mr-2">
                      {renderStatusIndicator(tasks.analyze?.status)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 3: Improvement Analysis</h4>
                      <p className="text-xs text-gray-500">
                        Identifies improvement opportunities.
                      </p>
                    </div>
                    <div>
                      <button
                        onClick={handleAnalyzeMvcd}
                        disabled={!mvcdStatus?.mvcd_exists || tasks.analyze?.status === 'running'}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          !mvcdStatus?.mvcd_exists || tasks.analyze?.status === 'running'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {tasks.analyze?.status === 'running' ? 'Running...' : 'Start'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Agent selection */}
              <div className="mt-2 flex items-center">
                <label htmlFor="coding-agent" className="mr-1 text-xs font-medium text-gray-700">
                  Agent:
                </label>
                <select
                  id="coding-agent"
                  value={codingAgent}
                  onChange={handleCodingAgentChange}
                  className="block w-28 pl-2 pr-6 py-0.5 text-xs border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                  disabled={tasks.enrich?.status === 'running'}
                  title="Select which coding agent to use for enriching MVCD descriptions"
                >
                  <option value="augment">Augment</option>
                  <option value="cursor">Cursor</option>
                  <option value="chatgpt">ChatGPT</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-300 mb-3 bg-gray-100 rounded-t-lg">
            <nav className="-mb-px flex space-x-4 px-2 pt-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`${
                  activeTab === 'overview'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('codebase')}
                className={`${
                  activeTab === 'codebase'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Code Base
              </button>

              <button
                onClick={() => setActiveTab('ignore')}
                className={`${
                  activeTab === 'ignore'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                .ignore
              </button>

              <button
                onClick={() => setActiveTab('improvements')}
                className={`${
                  activeTab === 'improvements'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Improvements
              </button>
            </nav>
          </div>

          {/* Tab Content Container */}
          <div className="flex-1 overflow-hidden">

          {/* Overview Tab - Combined Directory and Statistics */}
          {activeTab === 'overview' && (
            <div className="h-full flex flex-col">
              {/* Two-column layout */}
              <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">

                {/* Left Column - Directory Structure */}
                <div className="bg-white shadow overflow-hidden sm:rounded-lg h-full flex flex-col">
                  <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                    <div>
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Project Directory Structure</h3>
                      <p className="mt-1 text-sm text-gray-600">Browse the complete directory structure of your project.</p>
                    </div>
                    {/* Directory Statistics */}
                    {projectDirectoryStructure && (
                      <div className="flex space-x-4 text-sm">
                        <div className="text-center">
                          <div className="text-lg font-semibold text-blue-600">{directoryStats.folders}</div>
                          <div className="text-xs text-gray-500">Folders</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-green-600">{directoryStats.files}</div>
                          <div className="text-xs text-gray-500">Files</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-purple-600">{directoryStats.totalLoc}</div>
                          <div className="text-xs text-gray-500">Lines of Code</div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex-1 p-4 overflow-hidden">
                    {directoryLoading && (
                      <div className="flex justify-center items-center h-full">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className="ml-2 text-gray-600">Loading directory structure...</span>
                      </div>
                    )}

                    {directoryError && (
                      <div className="bg-red-50 border-l-4 border-red-500 p-4">
                        <div className="text-red-700">{directoryError}</div>
                        <button
                          onClick={fetchDirectoryStructure}
                          className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          Retry
                        </button>
                      </div>
                    )}

                    {projectDirectoryStructure && !directoryLoading && (
                      <div className="h-full bg-gray-50 border border-gray-200 rounded-lg p-4 overflow-y-auto">
                        {renderDirectoryNode(projectDirectoryStructure)}
                      </div>
                    )}

                    {!projectDirectoryStructure && !directoryLoading && !directoryError && (
                      <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
                        <div className="text-yellow-700">No directory structure available. Click "Load Directory Structure" to load it.</div>
                        <button
                          onClick={fetchDirectoryStructure}
                          className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          Load Directory Structure
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Column - MVCD Statistics */}
                <div className="space-y-4 h-full overflow-y-auto">
                  {/* MVCD Status Cards */}
                  <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div className="px-4 py-3 border-b border-gray-200">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">MVCD Status</h3>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">MVCD File</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {mvcdStatus?.mvcd_exists ? 'Exists' : 'Not Found'}
                            </dd>
                          </div>
                        </div>
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">Last Updated</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {formatDate(mvcdStatus?.last_updated)}
                            </dd>
                          </div>
                        </div>
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">Total Entries</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {mvcdStatus?.total_entries || 0}
                            </dd>
                          </div>
                        </div>
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">Entries with Descriptions</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {mvcdStatus?.entries_with_descriptions || 0} / {mvcdStatus?.total_entries || 0}
                            </dd>
                          </div>
                        </div>
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">Average Confidence</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {formatPercentage(mvcdStatus?.average_confidence || 0)}
                            </dd>
                          </div>
                        </div>
                        <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                          <div className="px-4 py-3">
                            <dt className="text-sm font-medium text-gray-500 truncate">Total Lines of Code</dt>
                            <dd className="mt-1 text-2xl font-semibold text-gray-900">
                              {mvcdStatus?.total_loc || 0}
                            </dd>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Detailed Metrics Section */}
                  {mvcdStatus?.mvcd_exists && (
                    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                      <div className="px-4 py-3 border-b border-gray-200">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">Detailed Metrics</h3>
                      </div>
                      <div className="p-4">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                          {/* Frontend Metrics */}
                          <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-200">
                            <h4 className="text-lg font-medium mb-4 text-blue-900">Frontend</h4>
                            <div className="space-y-4">
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-blue-700">Entries</span>
                                  <span className="text-sm font-medium text-blue-900">{mvcdStatus?.frontend_entries || 0}</span>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-blue-700">Lines of Code</span>
                                  <span className="text-sm font-medium text-blue-900">{mvcdStatus?.frontend_loc || 0}</span>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-blue-700">Confidence</span>
                                  <span className="text-sm font-medium text-blue-900">{formatPercentage(mvcdStatus?.frontend_confidence || 0)}</span>
                                </div>
                                <div className="w-full bg-blue-200 rounded-full h-2.5 mt-2">
                                  <div
                                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                                    style={{ width: `${mvcdStatus?.frontend_confidence || 0}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Backend Metrics */}
                          <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-200">
                            <h4 className="text-lg font-medium mb-4 text-green-900">Backend</h4>
                            <div className="space-y-4">
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-green-700">Entries</span>
                                  <span className="text-sm font-medium text-green-900">{mvcdStatus?.backend_entries || 0}</span>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-green-700">Lines of Code</span>
                                  <span className="text-sm font-medium text-green-900">{mvcdStatus?.backend_loc || 0}</span>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between">
                                  <span className="text-sm font-medium text-green-700">Confidence</span>
                                  <span className="text-sm font-medium text-green-900">{formatPercentage(mvcdStatus?.backend_confidence || 0)}</span>
                                </div>
                                <div className="w-full bg-green-200 rounded-full h-2.5 mt-2">
                                  <div
                                    className="bg-green-600 h-2.5 rounded-full transition-all duration-300"
                                    style={{ width: `${mvcdStatus?.backend_confidence || 0}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}



          {/* Code Base Tab */}
          {activeTab === 'codebase' && (
            <div>
              <div className="bg-gray-50 shadow overflow-hidden sm:rounded-lg mb-3 border border-gray-300 w-full">
                <div className="px-3 py-2">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-base font-medium text-gray-900">Code Base Structure</h3>
                    {mvcdData && (
                      <button
                        onClick={handleRefreshMvcdData}
                        disabled={mvcdLoading}
                        className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white ${
                          mvcdLoading
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                        }`}
                        title="Refresh MVCD data from file"
                      >
                        {mvcdLoading ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Refreshing...
                          </>
                        ) : (
                          <>
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh
                          </>
                        )}
                      </button>
                    )}
                  </div>

                  {/* Directory Focus Selection */}
                  <div className="mt-2 mb-3">
                    <label htmlFor="directory-focus" className="block text-xs font-medium text-gray-700 mb-1">
                      Directory Focus
                    </label>
                    <select
                      id="directory-focus"
                      value={selectedDirectory}
                      onChange={handleDirectoryChange}
                      className="block w-full pl-2 pr-8 py-1 text-xs border-gray-400 bg-white focus:outline-none focus:ring-gray-500 focus:border-gray-500 rounded-md shadow-sm"
                    >
                      {directoryStructure.map((dir) => {
                        // Calculate indentation for display
                        const indentLevel = (dir.match(/\//g) || []).length;
                        const indentStr = '\u00A0\u00A0'.repeat(indentLevel);
                        const displayName = dir === '/' ? 'All Directories' : dir.split('/').pop();

                        return (
                          <option key={dir} value={dir}>
                            {indentStr}{displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>

                  {/* MVCD Content Display */}
                  {mvcdLoading ? (
                    <div className="flex justify-center items-center h-64">
                      <div className="text-gray-500">Loading MVCD data...</div>
                    </div>
                  ) : mvcdError ? (
                    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                      <div className="text-red-700">{mvcdError}</div>
                      <button
                        onClick={fetchMvcdData}
                        className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Retry
                      </button>
                    </div>
                  ) : !mvcdData ? (
                    <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
                      <div className="text-yellow-700">No MVCD data available. Please generate MVCD first.</div>
                      <button
                        onClick={fetchMvcdData}
                        className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Load MVCD Data
                      </button>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-md overflow-hidden shadow-sm w-full">
                      {/* Header */}
                      <div className="flex bg-gray-200 p-1 border-b border-gray-300 font-medium text-xs text-gray-700">
                        <div className="w-[12%] text-left">Element</div>
                        <div className="w-[15%] text-left">Path</div>
                        <div className="w-[7%] text-left">Type</div>
                        <div className="w-[6%] text-left">Status</div>
                        <div className="w-[30%] text-left">Description</div>
                        <div className="w-[10%] text-left">Dependencies</div>
                        <div className="w-[5%] text-right">Conf</div>
                        <div className="w-[5%] text-right">LOC</div>
                        <div className="w-[10%] text-right pr-2">Modified</div>
                      </div>

                      {/* Item count display */}
                      <div className="bg-blue-50 px-2 py-1 text-xs text-blue-700 border-b border-gray-300">
                        Showing {mvcdData.codebase.filter(item => isInSelectedDirectory(item.file)).length} of {mvcdData.codebase.length} items
                        {selectedDirectory !== '/' && ` (filtered by: ${selectedDirectory})`}
                      </div>

                      {/* Scrollable Content */}
                      <div className="overflow-y-auto" style={{ maxHeight: '600px' }}>
                        {mvcdData.codebase
                          .filter(item => isInSelectedDirectory(item.file))
                          .map((item, index) => {
                            // Calculate indentation level for display
                            const indentLevel = getIndentationLevel(item.file);
                            const indentStr = '\u00A0\u00A0'.repeat(indentLevel);

                            // Get element name
                            const elementName = item.element || 'Unknown';

                            // Get status (active/deprecated)
                            const status = item.status || 'active';

                            // Get confidence level
                            const confidence = item.confidence || 0;

                            // Get lines of code
                            const loc = item.loc || 0;

                            // Get description
                            const description = item.description || 'No description available';

                            // Get last modified timestamp
                            const lastModified = item.last_modified || null;

                            return (
                              <div
                                key={`${item.file}-${elementName}-${index}`}
                                className={`flex items-center py-0.5 px-1 text-xs ${index % 2 === 0 ? 'bg-white' : 'bg-gray-100'} ${getDirectoryBgColor(item.file)} border-b border-gray-300 hover:bg-blue-100`}
                              >
                                <div className={`w-[12%] truncate text-left ${getDirectoryColor(item.file)}`} title={`${item.file}:${elementName}`}>
                                  {indentStr}{elementName}
                                </div>
                                <div className={`w-[15%] truncate text-left ${getDirectoryColor(item.file)}`} title={item.file}>
                                  {item.file}
                                </div>
                                <div className="w-[7%] truncate text-left" title={item.type || 'Not specified'}>
                                  {item.type || '-'}
                                </div>
                                <div className="w-[6%] whitespace-nowrap text-left">
                                  <span className={`inline-block px-1 text-xs font-medium rounded-full ${
                                    status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {status}
                                  </span>
                                </div>
                                <div className="w-[30%] truncate text-left" title={description}>
                                  {description.substring(0, 50)}{description.length > 50 ? '...' : ''}
                                </div>
                                <div className="w-[10%] truncate text-left" title={item.dependencies ? item.dependencies.join(', ') : 'None'}>
                                  {item.dependencies && item.dependencies.length > 0
                                    ? item.dependencies.join(', ').substring(0, 20) + (item.dependencies.join(', ').length > 20 ? '...' : '')
                                    : '-'}
                                </div>
                                <div className="w-[5%] text-right whitespace-nowrap">
                                  <span className={`inline-block px-1 text-xs font-medium rounded-full ${
                                    confidence >= 70 ? 'bg-green-100 text-green-800' :
                                    confidence > 0 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {confidence}%
                                  </span>
                                </div>
                                <div className="w-[5%] text-right whitespace-nowrap">
                                  {loc}
                                </div>
                                <div className="w-[10%] text-right text-gray-500 text-xs truncate whitespace-nowrap pr-2" title={lastModified ? new Date(lastModified * 1000).toLocaleString() : 'Not available'}>
                                  {lastModified ? formatDate(lastModified) : 'N/A'}
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}



          {/* Ignore Tab */}
          {activeTab === 'ignore' && (
            <div>
              <div className="bg-gray-50 shadow overflow-hidden sm:rounded-lg mb-3 border border-gray-300 w-full">
                <div className="px-3 py-2">
                  <div className="flex justify-between items-center mb-2">
                    <div>
                      <h3 className="text-base font-medium text-gray-900">MVCD Ignore Patterns</h3>
                      <p className="mt-1 text-xs text-gray-500">
                        Configure which files and directories to exclude from MVCD analysis. Uncheck patterns to include them in the codebase scan.
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      {/* Reload button */}
                      <button
                        onClick={handleReloadIgnoreData}
                        disabled={ignoreLoading}
                        className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white ${
                          ignoreLoading
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                        }`}
                        title="Reload ignore patterns from file"
                      >
                        {ignoreLoading ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Loading...
                          </>
                        ) : (
                          <>
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Reload
                          </>
                        )}
                      </button>
                      
                      {/* Hard reload button - clears cache */}
                      <button
                        onClick={handleHardReloadIgnoreData}
                        disabled={ignoreLoading}
                        className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white ${
                          ignoreLoading
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
                        }`}
                        title="Hard reload - clear all cache and reload from file"
                      >
                        {ignoreLoading ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Loading...
                          </>
                        ) : (
                          <>
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4l-8 8 8 8" />
                            </svg>
                            Hard Reload
                          </>
                        )}
                      </button>
                      
                      {/* Save Changes button */}
                    {ignoreModified && (
                      <button
                        onClick={saveIgnoreData}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Save Changes
                      </button>
                    )}
                    </div>
                  </div>

                  {ignoreLoading && (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600 text-sm">Loading ignore patterns...</span>
                    </div>
                  )}

                  {ignoreError && (
                    <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-3">
                      <div className="text-red-700 text-sm">{ignoreError}</div>
                    </div>
                  )}

                  {ignoreData.length > 0 && !ignoreLoading && (
                    <div className="border border-gray-300 rounded-md overflow-hidden shadow-sm w-full">
                      {/* Header */}
                      <div className="flex bg-gray-200 p-1 border-b border-gray-300 font-medium text-xs text-gray-700">
                        <div className="w-[10%] text-center">Enabled</div>
                        <div className="w-[30%] text-left">Pattern</div>
                        <div className="w-[60%] text-left">Description</div>
                      </div>

                      {/* Item count display */}
                      <div className="bg-blue-50 px-2 py-1 text-xs text-blue-700 border-b border-gray-300">
                        {ignoreData.length} ignore patterns configured
                      </div>

                      {/* Scrollable Content */}
                      <div className="overflow-y-auto" style={{ maxHeight: '500px' }}>
                        {ignoreData.map((item, index) => {
                          // Generate description and color based on pattern
                          const getPatternInfo = (pattern) => {
                            if (pattern.includes('node_modules')) return { desc: 'Node.js dependencies', color: 'bg-blue-50' };
                            if (pattern.includes('__pycache__')) return { desc: 'Python cache files', color: 'bg-yellow-50' };
                            if (pattern.includes('.git/')) return { desc: 'Git repository files', color: 'bg-orange-50' };
                            if (pattern.includes('.venv/')) return { desc: 'Python virtual environment', color: 'bg-green-50' };
                            if (pattern.includes('*.test.') || pattern.includes('*.spec.')) return { desc: 'Test files', color: 'bg-purple-50' };
                            if (pattern.includes('*.stories.')) return { desc: 'Storybook files', color: 'bg-pink-50' };
                            if (pattern.includes('*.svg') || pattern.includes('*.png') || pattern.includes('*.ico')) return { desc: 'Image files', color: 'bg-indigo-50' };
                            if (pattern.includes('*.css') || pattern.includes('*.scss')) return { desc: 'Stylesheet files', color: 'bg-cyan-50' };
                            if (pattern.includes('*.lock')) return { desc: 'Lock files', color: 'bg-red-50' };
                            if (pattern.includes('.env')) return { desc: 'Environment files', color: 'bg-amber-50' };
                            if (pattern.includes('__init__.py')) return { desc: 'Python package init files', color: 'bg-lime-50' };
                            if (pattern.includes('assets/') || pattern.includes('styles/')) return { desc: 'Static assets', color: 'bg-teal-50' };
                            return { desc: 'Custom ignore pattern', color: 'bg-gray-50' };
                          };

                          const patternInfo = getPatternInfo(item.pattern);
                          const rowBgColor = item.enabled 
                            ? (index % 2 === 0 ? 'bg-white' : patternInfo.color)
                            : 'bg-gray-100 opacity-60';

                          return (
                            <div
                              key={item.id}
                              className={`flex items-center py-0.5 px-1 text-xs ${rowBgColor} border-b border-gray-300 hover:bg-blue-100`}
                            >
                              <div className="w-[10%] text-center">
                                <input
                                  type="checkbox"
                                  checked={item.enabled}
                                  onChange={() => handleIgnoreToggle(item.id)}
                                  className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="w-[30%] truncate text-left font-mono" title={item.pattern}>
                                <span className={item.enabled ? 'text-gray-900' : 'text-gray-500'}>
                                  {item.pattern}
                                </span>
                              </div>
                              <div className="w-[60%] truncate text-left" title={patternInfo.desc}>
                                <span className={item.enabled ? 'text-gray-600' : 'text-gray-400'}>
                                  {patternInfo.desc}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {ignoreModified && (
                    <div className="mt-3 bg-yellow-50 border-l-4 border-yellow-500 p-3">
                      <div className="text-yellow-700 text-sm">
                        You have unsaved changes. Click "Save Changes" to apply your modifications.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Improvements Tab */}
          {activeTab === 'improvements' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-2 flex-shrink-0">
                <div className="px-3 py-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-base leading-6 font-medium text-gray-900">Improvement Suggestions</h3>
                      <p className="mt-1 text-xs text-gray-500">
                        Suggestions for improving the codebase based on MVCD analysis.
                      </p>
                    </div>
                    <div className="flex space-x-2 items-center">
                      <div className="flex items-center space-x-2">
                        <label className="text-xs font-medium text-gray-700">File:</label>
                        <select
                          value={selectedImprovementFile || ''}
                          onChange={(e) => handleImprovementFileChange(e.target.value)}
                          disabled={filesLoading || improvementFiles.length === 0}
                          className="block text-xs border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 min-w-0 flex-1"
                        >
                          {improvementFiles.length === 0 ? (
                            <option value="">No files available</option>
                          ) : (
                            <>
                              <option value="">All files</option>
                              {improvementFiles.map((file) => (
                                <option key={file.name} value={file.name}>
                                  {file.name} ({new Date(file.last_modified * 1000).toLocaleDateString()})
                                </option>
                              ))}
                            </>
                          )}
                        </select>
                      </div>
                      <button
                        onClick={executeSelectedImprovements}
                        disabled={executingImprovement || selectedImprovements.size === 0}
                        className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white ${
                          executingImprovement || selectedImprovements.size === 0
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                        }`}
                      >
                        {executingImprovement ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                            Executing...
                          </>
                        ) : (
                          <>Execute Selected ({selectedImprovements.size})</>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {(improvementsLoading || filesLoading) && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">
                    {filesLoading ? 'Loading improvement files...' : 'Loading improvements...'}
                  </span>
                </div>
              )}

              {improvementsError && (
                <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                  <div className="text-red-700">{improvementsError}</div>
                </div>
              )}

              {improvementsData && !improvementsLoading && !filesLoading && (
                <div>
                  {improvementsData.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-gray-500">No improvement suggestions found.</div>
                      <div className="text-sm text-gray-400 mt-2">
                        Run Step 3 of the MVCD workflow to generate improvement suggestions.
                      </div>
                    </div>
                  ) : (
                    <div className="overflow-auto" style={{ maxHeight: '600px' }}>
                      <div className="shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table className="min-w-full divide-y divide-gray-300">
                          <thead className="bg-gray-50 sticky top-0 z-10">
                            <tr>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                                File
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                Element
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                LOC
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                Category
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                Priority
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">
                                <input
                                  type="checkbox"
                                  checked={improvementsData.length > 0 && selectedImprovements.size === improvementsData.length}
                                  onChange={(e) => handleSelectAllImprovements(e.target.checked)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </th>
                              <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                Analyzed
                              </th>
                            </tr>
                          </thead>
                            <tbody className="bg-white divide-y divide-gray-100">
                              {improvementsData.map((item) => {
                                // Get category color
                                const getCategoryColor = (category) => {
                                  switch (category) {
                                    case 'clarity': return 'bg-yellow-100 text-yellow-800';
                                    case 'structure': return 'bg-blue-100 text-blue-800';
                                    case 'performance': return 'bg-green-100 text-green-800';
                                    case 'security': return 'bg-red-100 text-red-800';
                                    case 'redundancy': return 'bg-orange-100 text-orange-800';
                                    case 'semantics': return 'bg-purple-100 text-purple-800';
                                    case 'unused': return 'bg-gray-100 text-gray-800';
                                    case 'refactor': return 'bg-indigo-100 text-indigo-800';
                                    case 'cleanup': return 'bg-pink-100 text-pink-800';
                                    default: return 'bg-gray-100 text-gray-800';
                                  }
                                };

                                // Get priority color
                                const getPriorityColor = (priority) => {
                                  switch (priority) {
                                    case '1': return 'bg-red-100 text-red-800 border-red-200';
                                    case '2': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
                                    case '3': return 'bg-blue-100 text-blue-800 border-blue-200';
                                    case 'done': return 'bg-green-100 text-green-800 border-green-200';
                                    default: return 'bg-gray-100 text-gray-800 border-gray-200';
                                  }
                                };

                                const formatTimestamp = (timestamp) => {
                                  if (!timestamp) return '-';
                                  return new Date(timestamp).toLocaleDateString();
                                };

                                // Get file LOC from MVCD data if available
                                const getFileLOC = (filename) => {
                                  if (mvcdData && mvcdData.codebase) {
                                    const entry = mvcdData.codebase.find(entry => entry.file === filename);
                                    return entry ? entry.loc : null;
                                  }
                                  return null;
                                };

                                return (
                                  <tr key={item.id} className="hover:bg-gray-50 transition-colors duration-150">
                                    <td className="px-2 py-1 text-xs text-gray-900 font-mono">
                                      <div className="max-w-24 truncate" title={item.file}>
                                        {item.file}
                                      </div>
                                    </td>
                                    <td className="px-2 py-1 text-xs text-gray-600">
                                      <div className="max-w-20 truncate" title={item.element}>
                                        {item.element || '-'}
                                      </div>
                                    </td>
                                    <td className="px-2 py-1 text-xs text-gray-500 text-center">
                                      {getFileLOC(item.file) || '-'}
                                    </td>
                                    <td className="px-2 py-1 text-xs text-gray-900">
                                      <div className="break-words" title={item.description}>
                                        {item.description}
                                      </div>
                                    </td>
                                    <td className="px-2 py-1">
                                      <span className={`inline-flex items-center px-1 py-0.5 rounded text-xs font-medium ${getCategoryColor(item.category)}`}>
                                        {item.category}
                                      </span>
                                    </td>
                                    <td className="px-2 py-1">
                                      <select
                                        value={item.priority || ''}
                                        onChange={(e) => handlePriorityChange(item.id, e.target.value)}
                                        className={`block w-full text-xs border rounded px-1 py-0.5 ${getPriorityColor(item.priority)}`}
                                      >
                                        <option value="">-</option>
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="done">✓</option>
                                      </select>
                                    </td>
                                    <td className="px-2 py-1">
                                      <input
                                        type="checkbox"
                                        checked={selectedImprovements.has(item.id)}
                                        onChange={(e) => handleImprovementSelection(item.id, e.target.checked)}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                      />
                                    </td>
                                    <td className="px-2 py-1 text-xs text-gray-500">
                                      {formatTimestamp(item.suggestion_timestamp)}
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
            </div>
          )}
          </div>
        </div>
      )}

      {/* Recreate Confirmation Dialog */}
      {showRecreateConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mt-2">Recreate MVCD</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  This will completely rebuild the MVCD file, including re-enriching all descriptions. 
                  This process may take several minutes and will overwrite any existing enriched content.
                </p>
                <p className="text-sm text-red-600 mt-2 font-medium">
                  Are you sure you want to proceed?
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowRecreateConfirm(false)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleRecreateMvcd}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                  >
                    Recreate
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MVCD;

