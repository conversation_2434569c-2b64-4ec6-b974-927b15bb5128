import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import './index.css'
import './App.css'

// Components
import Layout from './components/Layout'
import AppLayout from './components/AppLayout'
import Projects from './components/Projects'
import Architecture from './components/Architecture'
import MVCD from './components/MVCD'
import Techstack from './components/Techstack'
import ProjectSelection from './components/ProjectSelection'
import ApiTest from './components/ApiTest'
import ClearStorage from './components/ClearStorage'

// Context
import { ProjectProvider, ProjectContext } from './contexts/ProjectContext'
import { useProject } from './hooks/useProject'
import { CodingAgentProvider } from './contexts/CodingAgentContext'
import { NotificationProvider } from './contexts/NotificationContext'

// Placeholder components for routes we haven't implemented yet
const Tasks = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">Tasks</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>Task management functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

const LLMInterface = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">LLM Interface</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>LLM integration functionality will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

const Visualization = () => {
  const { currentProject } = useProject();
  const [activeView, setActiveView] = useState('overview');
  const [workflowData, setWorkflowData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load workflow description data
  const loadWorkflowData = async () => {
    if (!currentProject) return;
    
    setLoading(true);
    setError(null);
    
    try {
      console.log('Loading workflow data for project:', currentProject);
      
      // Try to fetch the workflowDescription.yaml data
      const projectPath = encodeURIComponent(currentProject.path);
      console.log('Encoded project path:', projectPath);
      
      const response = await fetch(`/api/files/workflow-description?project=${projectPath}`);
      
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);
      
      if (response.ok) {
        const result = await response.json();
        console.log('Successfully loaded workflow data:', result);
        setWorkflowData(result.data); // Extract the data from the response
      } else {
        // Get error details from response
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorMessage;
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
        }
        
        console.warn('Failed to load workflow data:', errorMessage);
        
        // For now, use mock data structure based on our workflowDescription.yaml
        setWorkflowData({
          metadata: {
            version: "1.0.0",
            coverage_analysis: "Complete application workflow mapping"
          },
          workflow_coverage_analysis: {
            coverage_metrics: {
              total_files_in_codebase: 89,
              files_mapped_to_workflows: 71,
              coverage_percentage: 79.8,
              breakdown_by_status: {
                active_production_code: 65,
                deprecated_or_backup: 12,
                utility_and_scripts: 8,
                unmapped_unclear: 4
              }
            }
          },
          dead_code_analysis: {
            deprecated_directories: {
              high_confidence_removal: [
                { directory: "frontend/", reason: "Legacy React setup, replaced by frontend-vite", risk: "Low" },
                { directory: "test-frontend/", reason: "Test directory with empty files", risk: "None" },
                { directory: "src/", reason: "Standalone components not integrated", risk: "Low" }
              ]
            }
          },
          user_interaction_workflows: {
            project_selection_flow: { description: "User selects or creates a project workspace" },
            dashboard_navigation_flow: { description: "User navigates through main application areas" },
            architecture_visualization_flow: { description: "User generates and views architecture diagrams" },
            mvcd_analysis_flow: { description: "User triggers MVCD analysis" }
          }
        });
        setError(`Using fallback data - ${errorMessage}`);
      }
    } catch (err) {
      console.error('Error loading workflow data:', err);
      // Use mock data as fallback
      setWorkflowData({
        metadata: {
          version: "1.0.0",
          coverage_analysis: "Complete application workflow mapping"
        },
        workflow_coverage_analysis: {
          coverage_metrics: {
            total_files_in_codebase: 89,
            files_mapped_to_workflows: 71,
            coverage_percentage: 79.8,
            breakdown_by_status: {
              active_production_code: 65,
              deprecated_or_backup: 12,
              utility_and_scripts: 8,
              unmapped_unclear: 4
            }
          }
        },
        dead_code_analysis: {
          deprecated_directories: {
            high_confidence_removal: [
              { directory: "frontend/", reason: "Legacy React setup, replaced by frontend-vite", risk: "Low" },
              { directory: "test-frontend/", reason: "Test directory with empty files", risk: "None" },
              { directory: "src/", reason: "Standalone components not integrated", risk: "Low" }
            ]
          }
        },
        user_interaction_workflows: {
          project_selection_flow: { description: "User selects or creates a project workspace" },
          dashboard_navigation_flow: { description: "User navigates through main application areas" },
          architecture_visualization_flow: { description: "User generates and views architecture diagrams" },
          mvcd_analysis_flow: { description: "User triggers MVCD analysis" }
        }
      });
      setError(`Network error - using fallback data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWorkflowData();
  }, [currentProject]);

  // Load Mermaid dynamically
  useEffect(() => {
    const loadMermaid = async () => {
      if (typeof window !== 'undefined' && !window.mermaid) {
        try {
          const mermaid = await import('mermaid');
          mermaid.default.initialize({ 
            startOnLoad: false,
            theme: 'default',
            themeVariables: {
              primaryColor: '#3B82F6',
              primaryTextColor: '#1F2937',
              primaryBorderColor: '#E5E7EB',
              lineColor: '#6B7280',
              secondaryColor: '#F3F4F6',
              tertiaryColor: '#FAFAFA'
            }
          });
          window.mermaid = mermaid.default;
        } catch (error) {
          console.error('Failed to load Mermaid:', error);
        }
      }
    };
    loadMermaid();
  }, []);

  // Render Mermaid diagrams
  useEffect(() => {
    if (window.mermaid && workflowData) {
      // Small delay to ensure DOM is ready
      setTimeout(() => {
        try {
          window.mermaid.init();
        } catch (error) {
          console.error('Mermaid initialization error:', error);
        }
      }, 100);
    }
  }, [activeView, workflowData]);

  const viewTabs = [
    { id: 'overview', name: 'Coverage Overview', icon: '📊' },
    { id: 'workflows', name: 'Visual Workflows', icon: '🔄' },
    { id: 'startup', name: 'Startup Flow', icon: '🚀' },
    { id: 'dataflow', name: 'User Flows', icon: '📡' },
    { id: 'dependencies', name: 'Dependencies', icon: '🔗' },
    { id: 'deadcode', name: 'Dead Code Analysis', icon: '🗑️' }
  ];

  const CoverageOverview = () => {
    if (!workflowData?.workflow_coverage_analysis) return null;
    
    const metrics = workflowData.workflow_coverage_analysis.coverage_metrics;
    const breakdown = metrics.breakdown_by_status;
    
    return (
      <div className="space-y-6">
        {/* Coverage Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <div className="flex items-center">
              <div className="text-blue-600">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-600">Coverage</p>
                <p className="text-2xl font-semibold text-blue-900">{metrics.coverage_percentage}%</p>
              </div>
            </div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-6 border border-green-200">
            <div className="flex items-center">
              <div className="text-green-600">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-green-600">Active Files</p>
                <p className="text-2xl font-semibold text-green-900">{breakdown.active_production_code}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-red-50 rounded-lg p-6 border border-red-200">
            <div className="flex items-center">
              <div className="text-red-600">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"/>
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-red-600">Deprecated</p>
                <p className="text-2xl font-semibold text-red-900">{breakdown.deprecated_or_backup}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="text-gray-600">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Files</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.total_files_in_codebase}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Coverage Bar Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">File Status Breakdown</h3>
          <div className="space-y-4">
            {Object.entries(breakdown).map(([status, count]) => {
              const percentage = (count / metrics.total_files_in_codebase) * 100;
              const colors = {
                active_production_code: 'bg-green-500',
                deprecated_or_backup: 'bg-red-500',
                utility_and_scripts: 'bg-blue-500',
                unmapped_unclear: 'bg-gray-500'
              };
              
              return (
                <div key={status} className="flex items-center">
                  <div className="w-48 text-sm font-medium text-gray-700 capitalize">
                    {status.replace(/_/g, ' ')}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="bg-gray-200 rounded-full h-4">
                      <div 
                        className={`h-4 rounded-full ${colors[status]}`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                  <div className="w-16 text-sm text-gray-600 text-right">
                    {count} files
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const VisualWorkflows = () => {
    if (!workflowData) return <div className="text-gray-500">No workflow data available</div>;
    
    // Generate startup workflow diagram
    const generateStartupDiagram = () => {
      if (!workflowData.startup_workflows) return '';
      
      let diagram = 'graph TD\n';
      let nodeId = 1;
      const nodeMap = new Map();
      
      const addNode = (label, type = 'default') => {
        const id = `N${nodeId++}`;
        nodeMap.set(label, id);
        const sanitizedLabel = label.replace(/[^\w\s]/g, '').substring(0, 30);
        diagram += `    ${id}[${sanitizedLabel}]\n`;
        
        // Add styling based on type
        if (type === 'trigger') diagram += `    style ${id} fill:#e1f5fe\n`;
        else if (type === 'backend') diagram += `    style ${id} fill:#fff3e0\n`;
        else if (type === 'frontend') diagram += `    style ${id} fill:#e8f5e8\n`;
        
        return id;
      };
      
      // Process cold start sequence
      if (workflowData.startup_workflows.cold_start_sequence?.execution_path) {
        const steps = workflowData.startup_workflows.cold_start_sequence.execution_path;
        let previousId = null;
        
        steps.forEach((step, index) => {
          let nodeLabel = '';
          let nodeType = 'default';
          
          if (step.trigger) {
            nodeLabel = step.trigger;
            nodeType = 'trigger';
          } else if (step.component) {
            nodeLabel = step.component.split('::')[0] || step.component;
            nodeType = step.component.includes('backend/') ? 'backend' : 'frontend';
          }
          
          if (nodeLabel) {
            const currentId = addNode(nodeLabel, nodeType);
            if (previousId) {
              diagram += `    ${previousId} --> ${currentId}\n`;
            }
            previousId = currentId;
          }
        });
      }
      
      return diagram;
    };

    // Generate user interaction workflow diagram  
    const generateUserWorkflowDiagram = () => {
      if (!workflowData.user_interaction_workflows) return '';
      
      let diagram = 'graph TD\n';
      let nodeId = 1;
      const nodeMap = new Map();
      
      const addNode = (label, type = 'default') => {
        const id = `N${nodeId++}`;
        const sanitizedLabel = label.replace(/[^\w\s]/g, '').substring(0, 25);
        diagram += `    ${id}[${sanitizedLabel}]\n`;
        
        if (type === 'ui') diagram += `    style ${id} fill:#e3f2fd\n`;
        else if (type === 'backend') diagram += `    style ${id} fill:#fff3e0\n`;
        else if (type === 'action') diagram += `    style ${id} fill:#f3e5f5\n`;
        
        return id;
      };
      
      // Process each workflow
      Object.entries(workflowData.user_interaction_workflows).forEach(([workflowName, workflow]) => {
        if (workflow.execution_path) {
          let previousId = null;
          const workflowStartId = addNode(workflowName.replace(/_/g, ' '), 'action');
          previousId = workflowStartId;
          
          workflow.execution_path.forEach((step) => {
            if (step.trigger) {
              const triggerId = addNode(step.trigger.substring(0, 30), 'ui');
              diagram += `    ${previousId} --> ${triggerId}\n`;
              previousId = triggerId;
            }
            
            if (step.ui) {
              const uiId = addNode(step.ui.split('::')[0] || step.ui, 'ui');
              diagram += `    ${previousId} --> ${uiId}\n`;
              previousId = uiId;
            }
            
            if (step.backend) {
              const backendId = addNode(step.backend.split('::')[0] || step.backend, 'backend');
              diagram += `    ${previousId} --> ${backendId}\n`;
              previousId = backendId;
            }
          });
        }
      });
      
      return diagram;
    };

    // Generate backend processing diagram
    const generateBackendDiagram = () => {
      if (!workflowData.backend_processing_workflows) return '';
      
      let diagram = 'sequenceDiagram\n';
      diagram += '    participant U as User\n';
      diagram += '    participant F as Frontend\n';
      diagram += '    participant A as API\n';
      diagram += '    participant B as Backend\n';
      diagram += '    participant E as External\n\n';
      
      // Process backend workflows
      Object.values(workflowData.backend_processing_workflows).forEach((workflow) => {
        if (workflow.execution_path) {
          workflow.execution_path.forEach((step) => {
            if (step.trigger && step.calls) {
              const action = step.action || 'Process';
              diagram += `    U->>F: ${step.trigger.substring(0, 30)}\n`;
              diagram += `    F->>A: ${action.substring(0, 30)}\n`;
              diagram += `    A->>B: ${step.calls.substring(0, 30)}\n`;
              if (step.result) {
                diagram += `    B-->>F: ${step.result.substring(0, 30)}\n`;
              }
            }
          });
        }
      });
      
      return diagram;
    };
    
    return (
      <div className="space-y-8">
        {/* Startup Workflow */}
        {workflowData.startup_workflows && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Startup Workflows</h3>
            <div className="overflow-x-auto">
              <div className="mermaid" key={`startup-${currentProject?.path}`}>
                {generateStartupDiagram()}
              </div>
            </div>
          </div>
        )}

        {/* User Interaction Workflows */}
        {workflowData.user_interaction_workflows && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">User Interaction Workflows</h3>
            <div className="overflow-x-auto">
              <div className="mermaid" key={`user-${currentProject?.path}`}>
                {generateUserWorkflowDiagram()}
              </div>
            </div>
          </div>
        )}

        {/* Backend Processing */}
        {workflowData.backend_processing_workflows && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Backend Processing Flows</h3>
            <div className="overflow-x-auto">
              <div className="mermaid" key={`backend-${currentProject?.path}`}>
                {generateBackendDiagram()}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const ProjectDependencies = () => {
    if (!workflowData) return <div className="text-gray-500">No dependency data available</div>;
    
    // Generate dependency diagram from workflow data
    const generateDependencyDiagram = () => {
      let diagram = 'graph LR\n';
      let nodeId = 1;
      
      const addNode = (label, type = 'default') => {
        const id = `N${nodeId++}`;
        const sanitizedLabel = label.replace(/[^\w\s]/g, '').substring(0, 20);
        diagram += `    ${id}[${sanitizedLabel}]\n`;
        
        if (type === 'frontend') diagram += `    style ${id} fill:#e3f2fd\n`;
        else if (type === 'backend') diagram += `    style ${id} fill:#fff3e0\n`;
        else if (type === 'external') diagram += `    style ${id} fill:#fce4ec\n`;
        
        return id;
      };
      
      // Extract dependencies from startup workflows
      if (workflowData.startup_workflows?.dependency_initialization) {
        const deps = workflowData.startup_workflows.dependency_initialization;
        
        const frontendNode = addNode('Frontend App', 'frontend');
        const backendNode = addNode('Backend API', 'backend');
        
        // Add frontend dependencies
        if (deps.frontend_dependencies) {
          deps.frontend_dependencies.forEach((dep) => {
            const depNode = addNode(dep, 'frontend');
            diagram += `    ${depNode} --> ${frontendNode}\n`;
          });
        }
        
        // Add backend dependencies  
        if (deps.backend_dependencies) {
          deps.backend_dependencies.forEach((dep) => {
            const depNode = addNode(dep, 'backend');
            diagram += `    ${depNode} --> ${backendNode}\n`;
          });
        }
        
        diagram += `    ${frontendNode} --> ${backendNode}\n`;
      }
      
      return diagram;
    };
    
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Project Dependencies</h3>
          <div className="overflow-x-auto">
            <div className="mermaid" key={`deps-${currentProject?.path}`}>
              {generateDependencyDiagram()}
            </div>
          </div>
        </div>
        
        {/* Dependency Lists */}
        {workflowData.startup_workflows?.dependency_initialization && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {workflowData.startup_workflows.dependency_initialization.frontend_dependencies && (
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-3">Frontend Dependencies</h4>
                <ul className="space-y-1">
                  {workflowData.startup_workflows.dependency_initialization.frontend_dependencies.map((dep, idx) => (
                    <li key={idx} className="text-sm text-blue-800">• {dep}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {workflowData.startup_workflows.dependency_initialization.backend_dependencies && (
              <div className="bg-orange-50 rounded-lg p-4">
                <h4 className="font-medium text-orange-900 mb-3">Backend Dependencies</h4>
                <ul className="space-y-1">
                  {workflowData.startup_workflows.dependency_initialization.backend_dependencies.map((dep, idx) => (
                    <li key={idx} className="text-sm text-orange-800">• {dep}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const AppStartupFlow = () => {
    if (!workflowData?.startup_workflows) return <div className="text-gray-500">No startup workflow data available</div>;
    
    return (
      <div className="space-y-6">
        {/* Cold Start Sequence Details */}
        {workflowData.startup_workflows.cold_start_sequence && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Cold Start Sequence</h3>
            <p className="text-gray-600 mb-4">{workflowData.startup_workflows.cold_start_sequence.description}</p>
            
            <div className="space-y-3">
              {workflowData.startup_workflows.cold_start_sequence.execution_path?.map((step, idx) => (
                <div key={idx} className="border-l-4 border-blue-200 pl-4 py-2">
                  <div className="font-medium text-gray-900">
                    {step.trigger || step.component || step.action}
                  </div>
                  {step.action && (
                    <div className="text-sm text-gray-600 mt-1">{step.action}</div>
                  )}
                  {step.result && (
                    <div className="text-sm text-blue-600 mt-1">→ {step.result}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Dependency Initialization */}
        {workflowData.startup_workflows.dependency_initialization && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Dependency Initialization</h3>
            <p className="text-gray-600 mb-4">{workflowData.startup_workflows.dependency_initialization.description}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {workflowData.startup_workflows.dependency_initialization.backend_dependencies && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Backend Dependencies</h4>
                  <ul className="space-y-1">
                    {workflowData.startup_workflows.dependency_initialization.backend_dependencies.map((dep, idx) => (
                      <li key={idx} className="text-sm text-gray-600">• {dep}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {workflowData.startup_workflows.dependency_initialization.frontend_dependencies && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Frontend Dependencies</h4>
                  <ul className="space-y-1">
                    {workflowData.startup_workflows.dependency_initialization.frontend_dependencies.map((dep, idx) => (
                      <li key={idx} className="text-sm text-gray-600">• {dep}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  const DataFlowDiagram = () => {
    if (!workflowData?.user_interaction_workflows) return <div className="text-gray-500">No user interaction data available</div>;
    
    return (
      <div className="space-y-6">
        {/* User Workflow Details */}
        {Object.entries(workflowData.user_interaction_workflows).map(([workflowName, workflow]) => (
          <div key={workflowName} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 capitalize">
              {workflowName.replace(/_/g, ' ')}
            </h3>
            <p className="text-gray-600 mb-4">{workflow.description}</p>
            
            {workflow.execution_path && (
              <div className="space-y-3">
                {workflow.execution_path.map((step, idx) => (
                  <div key={idx} className="border rounded-lg p-4 bg-gray-50">
                    {step.trigger && (
                      <div className="font-medium text-blue-900">🔸 {step.trigger}</div>
                    )}
                    {step.ui && (
                      <div className="text-sm text-green-700 mt-1">📱 UI: {step.ui}</div>
                    )}
                    {step.action && (
                      <div className="text-sm text-purple-700 mt-1">⚡ Action: {step.action}</div>
                    )}
                    {step.backend && (
                      <div className="text-sm text-orange-700 mt-1">🔧 Backend: {step.backend}</div>
                    )}
                    {step.result && (
                      <div className="text-sm text-gray-600 mt-1">✅ Result: {step.result}</div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const DeadCodeAnalysis = () => {
    if (!workflowData?.dead_code_analysis) return null;
    
    const deadCodeData = workflowData.dead_code_analysis;
    const deprecatedDirs = deadCodeData.deprecated_directories?.high_confidence_removal || [];
    
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Deprecated Directories</h3>
          <div className="space-y-4">
            {deprecatedDirs.map((dir, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                      {dir.directory}
                    </h4>
                    <p className="mt-2 text-sm text-gray-600">
                      {dir.reason}
                    </p>
                    <div className="mt-3 flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        dir.risk === 'None' ? 'bg-green-100 text-green-800' :
                        dir.risk === 'Low' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        Risk: {dir.risk}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      Review
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Workflow Visualization</h1>
          <p className="text-sm text-gray-600 mt-2">
            {currentProject ? (
              <>
                Project: <span className="font-medium">{currentProject.name}</span> 
                <span className="text-gray-400 ml-2">({currentProject.path})</span>
              </>
            ) : (
              'Select a project to view workflow analysis'
            )}
          </p>
        </div>

        {currentProject ? (
          <>
            {/* Tab Navigation */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                {viewTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveView(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeView === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Content */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="text-gray-500">Loading workflow data...</div>
              </div>
            ) : error ? (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Workflow File Not Found
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        {error.includes('404') ? 
                          'This project does not have a workflowDescription.yaml file yet. You can generate one by running the workflow analysis tool.' :
                          error}
                      </p>
                      <p className="mt-2">Showing fallback data for demonstration purposes.</p>
                    </div>
                    {error.includes('404') && (
                      <div className="mt-4">
                        <button className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-2 rounded text-sm font-medium">
                          Generate Workflow File
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : null}

            {/* Content - Show even with errors using fallback data */}
            <div>
              {activeView === 'overview' && <CoverageOverview />}
              {activeView === 'workflows' && <VisualWorkflows />}
              {activeView === 'startup' && <AppStartupFlow />}
              {activeView === 'dataflow' && <DataFlowDiagram />}
              {activeView === 'dependencies' && <ProjectDependencies />}
              {activeView === 'deadcode' && <DeadCodeAnalysis />}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No Project Selected</h3>
            <p className="mt-1 text-sm text-gray-500">Select a project to view workflow visualization</p>
          </div>
        )}
      </div>
    </div>
  );
};

const Settings = () => (
  <div className="py-6">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
      <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
      <div className="py-4">
        <div className="bg-white shadow rounded-lg p-6">
          <p>Application settings will be implemented here.</p>
        </div>
      </div>
    </div>
  </div>
)

// Main application with project context
const AppContent = () => {
  const { currentProject } = useProject();

  return (
    <AppLayout>
      {/* Always show the main application with routing */}
      <Routes>
        <Route path="/" element={<Navigate to="/projects" replace />} />
        <Route path="/projects" element={<Layout><Projects /></Layout>} />
        <Route path="/mvcd" element={<Layout><MVCD /></Layout>} />
        <Route path="/techstack" element={<Layout><Techstack /></Layout>} />
        <Route path="/tasks" element={<Layout><Tasks /></Layout>} />
        <Route path="/llm" element={<Layout><LLMInterface /></Layout>} />
        <Route path="/visualization" element={<Layout><Visualization /></Layout>} />
        <Route path="/architecture" element={<Layout><Architecture /></Layout>} />
        <Route path="/settings" element={<Layout><Settings /></Layout>} />
        <Route path="/api-test" element={<Layout><ApiTest /></Layout>} />
        <Route path="/clear-storage" element={<Layout><ClearStorage /></Layout>} />
        <Route path="*" element={<Navigate to="/projects" replace />} />
      </Routes>
    </AppLayout>
  );
}

function App() {
  return (
    <Router>
      <NotificationProvider>
        <CodingAgentProvider>
          <ProjectProvider>
            <AppContent />
          </ProjectProvider>
        </CodingAgentProvider>
      </NotificationProvider>
    </Router>
  )
}

export default App
