#!/usr/bin/env python3

import requests
import json
import os
import shutil

# Test the complete project flow
BASE_URL = "http://localhost:7001/api"

# Test configuration
PROJECT_PATH = r"C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1"  # Update this path as needed

def test_complete_flow():
    """Test the complete project selection flow"""
    
    # Test path for a new project
    test_path = PROJECT_PATH
    
    print(f"=== Testing Complete Project Flow ===")
    print(f"Test project path: {test_path}")
    
    # Clean up if directory exists
    if os.path.exists(test_path):
        shutil.rmtree(test_path)
        print(f"✓ Cleaned up existing directory: {test_path}")
    
    # Step 1: Check if directory exists (should be False)
    print(f"\n--- Step 1: Check non-existent directory ---")
    try:
        response = requests.post(
            f"{BASE_URL}/projects/check",
            json={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Check response: {json.dumps(result, indent=2)}")
            
            if not result.get('hasVibeArchFolder', True):
                print("✓ Correctly detected no .VibeArch folder")
            else:
                print("✗ Incorrectly detected .VibeArch folder")
                return False
        else:
            print(f"✗ Check failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Check error: {e}")
        return False
    
    # Step 2: Initialize the project
    print(f"\n--- Step 2: Initialize project ---")
    try:
        response = requests.post(
            f"{BASE_URL}/projects/initialize",
            json={"path": test_path, "name": "Debug Test Project"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Initialize response: {json.dumps(result, indent=2)}")
            
            # Check if .VibeArch directory was created
            vibearch_dir = os.path.join(test_path, ".VibeArch")
            if os.path.exists(vibearch_dir):
                print(f"✓ .VibeArch directory created: {vibearch_dir}")
                
                # List contents
                contents = os.listdir(vibearch_dir)
                print(f"✓ .VibeArch contents: {contents}")
                
                # Check for VibeArch_Setup
                setup_dir = os.path.join(vibearch_dir, "VibeArch_Setup")
                if os.path.exists(setup_dir):
                    setup_contents = os.listdir(setup_dir)
                    print(f"✓ VibeArch_Setup contents: {setup_contents}")
                else:
                    print("✗ VibeArch_Setup directory not found")
            else:
                print(f"✗ .VibeArch directory not created: {vibearch_dir}")
                return False
        else:
            print(f"✗ Initialize failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Initialize error: {e}")
        return False
    
    # Step 3: Check directory again (should now have .VibeArch)
    print(f"\n--- Step 3: Check directory after initialization ---")
    try:
        response = requests.post(
            f"{BASE_URL}/projects/check",
            json={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Check response: {json.dumps(result, indent=2)}")
            
            if result.get('hasVibeArchFolder', False):
                print("✓ Correctly detected .VibeArch folder")
            else:
                print("✗ Failed to detect .VibeArch folder after initialization")
                return False
        else:
            print(f"✗ Check failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Check error: {e}")
        return False
    
    # Step 4: Load the project
    print(f"\n--- Step 4: Load project ---")
    try:
        response = requests.get(
            f"{BASE_URL}/projects/load",
            params={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Load response: {json.dumps(result, indent=2)}")
        else:
            print(f"✗ Load failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ Load error: {e}")
        return False
    
    print(f"\n=== Flow Test Completed Successfully ===")
    return True

def test_backend_health():
    """Test if backend is running"""
    print("=== Testing Backend Health ===")
    try:
        response = requests.get("http://localhost:7001/health", timeout=5)
        print(f"✓ Backend health: {response.status_code} - {response.text}")
        return True
    except Exception as e:
        print(f"✗ Backend health error: {e}")
        return False

if __name__ == "__main__":
    print("Starting debug tests...")
    
    # Test backend health first
    if not test_backend_health():
        print("Backend is not running. Please start the backend first.")
        exit(1)
    
    # Test complete flow
    if test_complete_flow():
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Some tests failed!") 