import { useState } from 'react';

const ClearStorage = () => {
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('');

  const clearLocalStorage = () => {
    try {
      // Show what's currently stored
      const currentProject = localStorage.getItem('currentProject');
      const recentProjects = localStorage.getItem('recentProjects');
      const backendApiUrl = localStorage.getItem('backendApiUrl');
      
      console.log('Before clearing:');
      console.log('currentProject:', currentProject);
      console.log('recentProjects:', recentProjects);
      console.log('backendApiUrl:', backendApiUrl);
      
      // Get the keys we want to clear
      const projectKeys = [
        'currentProject',
        'recentProjects',
        'backendApiUrl'  // Add this to clear any cached backend URL
      ];
      
      let clearedItems = [];
      
      // Clear only the project-related keys
      projectKeys.forEach(key => {
        if (localStorage.getItem(key)) {
          clearedItems.push(key);
          localStorage.removeItem(key);
        }
      });
      
      if (clearedItems.length > 0) {
        setMessage(`Successfully cleared: ${clearedItems.join(', ')}`);
        setStatus('success');
      } else {
        setMessage('No items found to clear');
        setStatus('info');
      }
      
      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      setMessage(`Error clearing localStorage: ${error.message}`);
      setStatus('error');
    }
  };

  const checkCurrentValues = () => {
    const backendApiUrl = localStorage.getItem('backendApiUrl');
    const currentProject = localStorage.getItem('currentProject');
    const recentProjects = localStorage.getItem('recentProjects');
    
    let info = 'Current localStorage values:\n';
    info += `backendApiUrl: ${backendApiUrl || 'not set'}\n`;
    info += `currentProject: ${currentProject ? 'exists' : 'not set'}\n`;
    info += `recentProjects: ${recentProjects ? 'exists' : 'not set'}`;
    
    setMessage(info);
    setStatus('info');
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Clear Project Data</h1>
      
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Clear Project Storage</h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>
              This will remove all saved project data from your browser's localStorage.
              Use this if you're seeing mock projects or experiencing issues with project selection.
            </p>
          </div>
          <div className="mt-5 space-x-3">
            <button
              type="button"
              onClick={checkCurrentValues}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Check Current Values
            </button>
            <button
              type="button"
              onClick={clearLocalStorage}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Clear Project Data
            </button>
          </div>
          
          {message && (
            <div className={`mt-4 p-4 rounded-md ${status === 'success' ? 'bg-green-50 text-green-700' : status === 'info' ? 'bg-blue-50 text-blue-700' : 'bg-red-50 text-red-700'}`}>
              <pre className="whitespace-pre-wrap text-sm">{message}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClearStorage;
