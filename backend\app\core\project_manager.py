import os
import shutil
from typing import List, Dict, Optional, Any
from pathlib import Path

from app.core.config import settings, ProjectSettings
from app.services.file_service import FileService
from app.models.project import Project
from app.models.task import Task

class ProjectManager:
    """Manages project directories and files"""

    def __init__(self):
        self.file_service = FileService()

    def initialize_project(self, project_path: str, project_name: Optional[str] = None) -> Project:
        """Initialize a new project directory with Vibe Architect files"""
        # Create absolute path
        abs_path = os.path.abspath(project_path)

        # Create project directory if it doesn't exist
        os.makedirs(abs_path, exist_ok=True)

        # Create .vibearch directory
        vibearch_dir = os.path.join(abs_path, settings.VIBEARCH_DIR_NAME)
        os.makedirs(vibearch_dir, exist_ok=True)

        # Create standard subdirectories
        directories = [
            os.path.join(vibearch_dir, "Directory"),
            os.path.join(vibearch_dir, "Specification"),
            os.path.join(vibearch_dir, "Specification", "history"),
            os.path.join(vibearch_dir, "Architecture"),
            os.path.join(vibearch_dir, "Architecture", "diagrams"),
            os.path.join(vibearch_dir, "TechStack"),
            os.path.join(vibearch_dir, "TechStack", "config"),
            os.path.join(vibearch_dir, "Documentation"),
            os.path.join(vibearch_dir, "Documentation", "api"),
            os.path.join(vibearch_dir, "Documentation", "user")
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        # Copy VibeArch_Setup folder from the main application directory
        self._copy_vibearch_setup(vibearch_dir)

        # Create default project settings
        project_settings = ProjectSettings()

        # Create default files if they don't exist
        self.file_service.write_settings(abs_path, project_settings)

        # Create specification file
        spec_path = os.path.join(vibearch_dir, "Specification", "main.md")
        if not os.path.exists(spec_path):
            with open(spec_path, 'w', encoding='utf-8') as f:
                f.write(f"# {project_name or 'Project'} Specification\n\n## Overview\n\nAdd your project overview here.\n")

        # Create tasks file
        if not os.path.exists(os.path.join(vibearch_dir, settings.TASKS_FILENAME)):
            self.file_service.write_tasks(abs_path, [])

        # Create architecture overview
        arch_overview_path = os.path.join(vibearch_dir, "Architecture", "overview.md")
        if not os.path.exists(arch_overview_path):
            with open(arch_overview_path, 'w', encoding='utf-8') as f:
                f.write(f"# {project_name or 'Project'} Architecture Overview\n\n## System Architecture\n\n[Provide a high-level description of the system architecture]\n\n## Components\n\n[Describe the main components of the system]\n")

        # Create architecture diagrams
        diagram_files = {
            "system_architecture.md": f"# System Architecture\n\n```mermaid\ngraph TD\n    A[{project_name or 'Project'}] --> B[Component 1]\n    A --> C[Component 2]\n    B --> D[Subcomponent 1.1]\n    B --> E[Subcomponent 1.2]\n    C --> F[Subcomponent 2.1]\n```\n",
            "frontend_architecture.md": f"# Frontend Architecture\n\n```mermaid\ngraph TD\n    A[Frontend] --> B[Component 1]\n    A --> C[Component 2]\n```\n",
            "backend_architecture.md": f"# Backend Architecture\n\n```mermaid\ngraph TD\n    A[Backend] --> B[Component 1]\n    A --> C[Component 2]\n```\n",
            "data_flow.md": f"# Data Flow\n\n```mermaid\ngraph LR\n    A[User] --> B[Frontend]\n    B --> C[API]\n    C --> D[Backend]\n    D --> E[Database]\n```\n"
        }

        for filename, content in diagram_files.items():
            file_path = os.path.join(vibearch_dir, "Architecture", "diagrams", filename)
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

        # Create tech stack file
        tech_stack_path = os.path.join(vibearch_dir, "TechStack", "stack.json")
        if not os.path.exists(tech_stack_path):
            with open(tech_stack_path, 'w', encoding='utf-8') as f:
                f.write('{\n  "frontend": [],\n  "backend": [],\n  "database": [],\n  "devops": []\n}')

        # Create initial techstack markdown file
        tech_stack_md_path = os.path.join(vibearch_dir, "TechStack", "techstack.md")
        if not os.path.exists(tech_stack_md_path):
            with open(tech_stack_md_path, 'w', encoding='utf-8') as f:
                f.write(f"""# Technology Stack Analysis

**Last Updated:** Not yet analyzed  
**Project:** {project_name or 'Project'}  
**Project Path:** {abs_path}

---

## Summary

This document will contain a comprehensive analysis of the technology stack used in this project once the techstack detection is run.

To generate the analysis:
1. Open the Vibe Architect interface
2. Navigate to the Techstack tab
3. Click "Refresh Detection" to analyze the project

---

## Notes

This file will be automatically updated when you run the techstack detection from the Vibe Architect interface.

*Placeholder created by Vibe Architect*
""")

        # Create documentation readme
        docs_readme_path = os.path.join(vibearch_dir, "Documentation", "readme.md")
        if not os.path.exists(docs_readme_path):
            with open(docs_readme_path, 'w', encoding='utf-8') as f:
                f.write(f"# {project_name or 'Project'} Documentation\n\n## Overview\n\nAdd your project documentation here.\n")

        # Return project info
        return self.get_project(abs_path)

    def _copy_vibearch_setup(self, target_vibearch_dir: str) -> None:
        """Copy VibeArch_Setup folder from the main application directory to the target project"""
        try:
            # Get the current working directory (where the main application is running)
            current_dir = os.getcwd()
            
            # Determine the project root directory
            # If we're running from backend/, go up one level to find the project root
            if current_dir.endswith('backend') or current_dir.endswith('backend\\'):
                project_root = os.path.dirname(current_dir)
            else:
                project_root = current_dir
            
            # Path to the source VibeArch_Setup folder (should be in project root)
            source_setup_dir = os.path.join(project_root, ".VibeArch", "VibeArch_Setup")
            
            # Path to the target VibeArch_Setup folder
            target_setup_dir = os.path.join(target_vibearch_dir, "VibeArch_Setup")
            
            print(f"Current working directory: {current_dir}")
            print(f"Project root directory: {project_root}")
            print(f"Looking for source VibeArch_Setup at: {source_setup_dir}")
            
            # Check if source directory exists, if not try alternative paths
            if not (os.path.exists(source_setup_dir) and os.path.isdir(source_setup_dir)):
                # Try alternative paths (for when server is run from different directories)
                alternative_paths = [
                    os.path.join(current_dir, ".VibeArch", "VibeArch_Setup"),  # Current directory
                    os.path.join(os.path.dirname(current_dir), ".VibeArch", "VibeArch_Setup"),  # Parent directory
                    os.path.join(current_dir, "backend", ".VibeArch", "VibeArch_Setup"),  # If running from root with backend subdir
                    os.path.join(os.path.dirname(current_dir), "backend", ".VibeArch", "VibeArch_Setup"),  # Parent with backend subdir
                ]
                
                print(f"Source directory not found at {source_setup_dir}, trying alternatives:")
                for alt_path in alternative_paths:
                    print(f"  Trying: {alt_path}")
                    if os.path.exists(alt_path) and os.path.isdir(alt_path):
                        source_setup_dir = alt_path
                        print(f"  Found at: {alt_path}")
                        break
                else:
                    print("  No alternative paths found")
            
            if os.path.exists(source_setup_dir) and os.path.isdir(source_setup_dir):
                # Remove existing directory first if it exists
                if os.path.exists(target_setup_dir):
                    shutil.rmtree(target_setup_dir)
                
                # Copy the entire VibeArch_Setup directory
                shutil.copytree(source_setup_dir, target_setup_dir)
                
                # List the copied files for verification
                copied_files = []
                for root, dirs, files in os.walk(target_setup_dir):
                    for file in files:
                        rel_path = os.path.relpath(os.path.join(root, file), target_setup_dir)
                        copied_files.append(rel_path)
                
                print(f"Successfully copied VibeArch_Setup from {source_setup_dir} to {target_setup_dir}")
                print(f"Copied files ({len(copied_files)}): {', '.join(copied_files)}")
                
            else:
                print(f"Warning: VibeArch_Setup directory not found at {source_setup_dir}")
                print("Available directories in project root:")
                try:
                    for item in os.listdir(project_root):
                        item_path = os.path.join(project_root, item)
                        if os.path.isdir(item_path):
                            print(f"  {item}/")
                except Exception as e:
                    print(f"  Error listing directories: {e}")
                
                # Create an empty VibeArch_Setup directory as fallback
                os.makedirs(target_setup_dir, exist_ok=True)
                
                # Create essential files as fallback
                self._create_fallback_setup_files(target_setup_dir)
                print(f"Created fallback VibeArch_Setup directory with essential files")
                
        except Exception as e:
            print(f"Error copying VibeArch_Setup: {e}")
            import traceback
            traceback.print_exc()
            # Create minimal setup as fallback
            target_setup_dir = os.path.join(target_vibearch_dir, "VibeArch_Setup")
            os.makedirs(target_setup_dir, exist_ok=True)
            self._create_fallback_setup_files(target_setup_dir)

    def _create_fallback_setup_files(self, target_setup_dir: str) -> None:
        """Create essential fallback files in the VibeArch_Setup directory"""
        
        # Create basic .mvcd-ignore.yaml file
        ignore_file_path = os.path.join(target_setup_dir, ".mvcd-ignore.yaml")
        if not os.path.exists(ignore_file_path):
            with open(ignore_file_path, 'w', encoding='utf-8') as f:
                f.write("""ignore:
  # Dependencies
  - node_modules/
  - __pycache__/
  - .venv/
  - venv/
  
  # Build outputs
  - dist/
  - build/
  - .next/
  
  # Version control
  - .git/
  
  # IDE files
  - .vscode/
  - .idea/
  
  # OS files
  - .DS_Store
  - Thumbs.db
  
  # Logs
  - "*.log"
  
  # Environment files
  - .env
  - .env.local
  - .env.production
  
  # Lock files
  - package-lock.json
  - yarn.lock
  - poetry.lock
""")

        # Create basic MVCD enrichment prompt
        prompt_file_path = os.path.join(target_setup_dir, "mvcd_description_enrichment_prompt.yaml")
        if not os.path.exists(prompt_file_path):
            with open(prompt_file_path, 'w', encoding='utf-8') as f:
                f.write("""# MVCD Description Enrichment Prompt

Please analyze the provided MVCD (Minimum Viable Code Description) file and enrich it with meaningful descriptions and confidence scores.

## Instructions:
1. For each entry without a description (or with "TODO: Add description"), provide a clear, concise description
2. Add confidence scores (0-100) based on how certain you are about the description
3. Preserve existing descriptions unless they can be significantly improved
4. Focus on what the code does and why it exists in the system

## Output:
Return only the updated MVCD YAML content with enriched descriptions and confidence scores.
""")

        # Create basic improvement analysis prompt
        improvement_file_path = os.path.join(target_setup_dir, "Improvement_Analysis.yaml")
        if not os.path.exists(improvement_file_path):
            with open(improvement_file_path, 'w', encoding='utf-8') as f:
                f.write("""# Improvement Analysis Prompt

Analyze the codebase and suggest improvements for code quality, performance, and maintainability.

## Focus Areas:
- Code structure and organization
- Performance optimizations
- Security considerations
- Best practices adherence
- Documentation improvements

## Output Format:
Provide structured improvement suggestions with priority levels and implementation guidance.
""")

        print(f"Created fallback files: .mvcd-ignore.yaml, mvcd_description_enrichment_prompt.yaml, Improvement_Analysis.yaml")

    def get_project(self, project_path: str) -> Project:
        """Get project information"""
        abs_path = os.path.abspath(project_path)
        vibearch_dir = os.path.join(abs_path, settings.VIBEARCH_DIR_NAME)

        if not os.path.exists(vibearch_dir):
            raise ValueError(f"Not a Vibe Architect project: {abs_path}")

        # Read project settings
        project_settings = self.file_service.read_settings(abs_path)

        # Get project name (directory name)
        project_name = os.path.basename(abs_path)

        # Read tasks
        tasks = self.file_service.read_tasks(abs_path)

        return Project(
            name=project_name,
            path=abs_path,
            settings=project_settings,
            tasks=tasks
        )

    def list_projects(self, base_directory: str) -> List[Project]:
        """List all Vibe Architect projects in the given directory"""
        projects = []

        # Check if the base directory itself is a project
        if os.path.exists(os.path.join(base_directory, settings.VIBEARCH_DIR_NAME)):
            projects.append(self.get_project(base_directory))
            return projects

        # Check subdirectories
        for item in os.listdir(base_directory):
            item_path = os.path.join(base_directory, item)
            if os.path.isdir(item_path):
                if os.path.exists(os.path.join(item_path, settings.VIBEARCH_DIR_NAME)):
                    try:
                        projects.append(self.get_project(item_path))
                    except Exception as e:
                        print(f"Error loading project {item_path}: {e}")

        return projects

    def update_project_settings(self, project_path: str, updated_settings: Dict[str, Any]) -> Project:
        """Update project settings"""
        # Get current settings
        current_settings = self.file_service.read_settings(project_path)

        # Update settings
        for key, value in updated_settings.items():
            if hasattr(current_settings, key):
                setattr(current_settings, key, value)

        # Write updated settings
        self.file_service.write_settings(project_path, current_settings)

        # Return updated project
        return self.get_project(project_path)
