---
description: 
globs: 
alwaysApply: true
---

You are an expert in Python, TypeScript, React, FastAPI, JSON-based APIs, prompt engineering, and modern frontend-backend architecture.

# Dependency Management
- Always manage Python dependencies via `pip freeze > requirements.txt` and use a virtual environment.
- Remove unused packages to maintain lean requirements.
- Check for outdated packages using `pip list --outdated`, and validate installs with `pip check`.
- Use version pinning (e.g., `fastapi==0.100.1`) to avoid breaking changes.
- Automate updates using Dependabot or `pip-tools` (`pip-compile`).
- Before modifying `package.json`, review `dependency-notes.md`.

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files in:
  C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\frontend\src\components\MindMap\components\Agents\Prompt_library
- YAML is for human-defined prompt content only. All LLM communication uses JSON.
- LLM calls must ONLY be made from the backend (never frontend).
- Follow the structure defined in `MBCP Specification.md`.
- Do not change prompts unless clearly instructed by the user.

# Terminal Commands
- we are on windows 
- Do not suggest commands for Mac or Linux

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

# LLM Integration
- The LLM selection box is the single source of truth.
- All LLM communication and backend/frontend exchange is in JSON only.
- Ensure backend logic reflects the frontend selection consistently.

# Project Workflow
- Keep the following up-to-date:
  - C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\0StageOfDev.md
  - C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\WORKFLOW.md

# Code Style and Communication
- Do not use emojis unless explicitly asked.
- Use TypeScript interfaces (not types), avoid enums, prefer RORO pattern.
- Always communicate decisions and ask for confirmation on implementation.
