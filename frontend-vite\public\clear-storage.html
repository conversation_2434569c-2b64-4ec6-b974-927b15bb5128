<!DOCTYPE html>
<html>
<head>
    <title>Clear Storage</title>
</head>
<body>
    <h1>Clear Storage</h1>
    <button onclick="clearStorage()">Clear All Storage</button>
    <div id="result"></div>
    
    <script>
        function clearStorage() {
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear specific keys that might cause issues
                localStorage.removeItem('backendApiUrl');
                
                document.getElementById('result').innerHTML = '<p>Storage cleared successfully! Please refresh your main app.</p>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p>Error clearing storage: ' + error.message + '</p>';
            }
        }
        
        // Show current storage contents
        document.addEventListener('DOMContentLoaded', function() {
            const storage = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage[key] = localStorage.getItem(key);
            }
            console.log('Current localStorage:', storage);
        });
    </script>
</body>
</html> 