import { useState, useEffect } from 'react';
import api from '../services/api';

const FileBrowser = ({ onSelect, onCancel }) => {
  const [currentPath, setCurrentPath] = useState('C:/Users/<USER>/Documents/VSCode');
  const [directories, setDirectories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch directories when the component mounts or the path changes
  useEffect(() => {
    fetchDirectories(currentPath);
  }, [currentPath]);

  const fetchDirectories = async (path) => {
    setLoading(true);
    setError(null);

    try {
      // Call the backend API to get directories
      console.log(`Fetching directories from: ${path}`);

      let data;

      // Use the proxy through the api service
      const response = await api.get(`/directory/list?path=${encodeURIComponent(path)}`);
      data = response.data;
      console.log('Directory API response:', data);

      if (data && Array.isArray(data.directories)) {
        setDirectories(data.directories);
      } else {
        throw new Error('Invalid response format from directory API');
      }
    } catch (err) {
      console.error('Error fetching directories:', err);

      // If the API call fails, fall back to mock data
      console.log('Falling back to mock data');
      const mockData = await getMockDirectories(path);
      setDirectories(mockData.directories);

      // Still show the error
      setError(`Error fetching directories: ${err.message || err}`);
    } finally {
      setLoading(false);
    }
  };

  // Fallback mock directory data
  const getMockDirectories = async (path) => {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock directory structure
    const mockDirectories = {
      'C:/': ['Users', 'Windows', 'Program Files', 'Program Files (x86)'],
      'C:/Users': ['Administrator', 'Public'],
      'C:/Users/<USER>': ['Documents', 'Downloads', 'Desktop', 'Pictures'],
      'C:/Users/<USER>/Documents': ['VSCode'],
      'C:/Users/<USER>/Documents/VSCode': ['Vibearch', 'Other Projects'],
      'C:/Users/<USER>/Documents/VSCode/Vibearch': ['.VibeArch', 'backend', 'frontend-vite'],
    };

    // Check if the path exists in our mock structure
    const pathKey = path.replace(/\\/g, '/');
    if (mockDirectories[pathKey]) {
      return {
        directories: mockDirectories[pathKey].map(dir => ({
          name: dir,
          path: `${pathKey}/${dir}`.replace(/\/\//g, '/'),
          isDirectory: true
        }))
      };
    }

    // If the path doesn't exist in our mock structure, return an empty array
    return { directories: [] };
  };

  const handleDirectoryClick = (dirPath) => {
    setCurrentPath(dirPath);
  };

  const handleGoUp = () => {
    // Go up one level in the directory structure
    const upPath = currentPath.split('/').slice(0, -1).join('/');
    if (upPath) {
      setCurrentPath(upPath);
    }
  };

  const handleSelect = () => {
    onSelect(currentPath);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 max-w-2xl mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Select Directory</h2>
        <button
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <button
            onClick={handleGoUp}
            className="px-2 py-1 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200"
            disabled={currentPath === 'C:/'}
          >
            ↑ Up
          </button>
          <div className="flex-1 px-3 py-2 border border-gray-300 rounded bg-gray-50 overflow-x-auto whitespace-nowrap">
            {currentPath}
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      <div className="border border-gray-300 rounded h-64 overflow-y-auto mb-4">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-gray-500">Loading...</div>
          </div>
        ) : directories.length === 0 ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-gray-500">No directories found</div>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {directories.map((dir) => (
              <li key={dir.path}>
                <button
                  onClick={() => handleDirectoryClick(dir.path)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center"
                >
                  <span className="mr-2">📁</span>
                  {dir.name}
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <button
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSelect}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Select
        </button>
      </div>
    </div>
  );
};

export default FileBrowser;
