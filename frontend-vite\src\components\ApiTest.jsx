import { useState } from 'react';
import axios from 'axios';

const ApiTest = () => {
  const [apiUrl, setApiUrl] = useState('http://localhost:7001/api');
  const [testPath, setTestPath] = useState('C:/Users/<USER>/Documents/VSCode/Vibearch');
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const testApi = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Test health endpoint
      const healthResponse = await axios.get(`${apiUrl.replace('/api', '')}/health`);
      console.log('Health check response:', healthResponse.data);

      // Test project check endpoint
      const checkResponse = await axios.post(`${apiUrl}/projects/check`, { path: testPath });
      console.log('Project check response:', checkResponse.data);
      
      setResult({
        health: healthResponse.data,
        check: checkResponse.data
      });
    } catch (err) {
      console.error('API test error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '600px', 
      margin: '0 auto', 
      backgroundColor: 'white',
      borderRadius: '0.5rem',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    }}>
      <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>API Test</h2>
      
      <div style={{ marginBottom: '1rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem' }}>
          API URL:
          <input 
            type="text" 
            value={apiUrl} 
            onChange={(e) => setApiUrl(e.target.value)}
            style={{ 
              display: 'block', 
              width: '100%', 
              padding: '0.5rem', 
              border: '1px solid #ccc', 
              borderRadius: '0.25rem',
              marginTop: '0.25rem'
            }}
          />
        </label>
      </div>
      
      <div style={{ marginBottom: '1rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem' }}>
          Test Path:
          <input 
            type="text" 
            value={testPath} 
            onChange={(e) => setTestPath(e.target.value)}
            style={{ 
              display: 'block', 
              width: '100%', 
              padding: '0.5rem', 
              border: '1px solid #ccc', 
              borderRadius: '0.25rem',
              marginTop: '0.25rem'
            }}
          />
        </label>
      </div>
      
      <button 
        onClick={testApi}
        disabled={loading}
        style={{ 
          backgroundColor: loading ? '#93C5FD' : '#2563EB',
          color: 'white',
          padding: '0.5rem 1rem',
          borderRadius: '0.25rem',
          border: 'none',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '1rem'
        }}
      >
        {loading ? 'Testing...' : 'Test API'}
      </button>
      
      {error && (
        <div style={{ 
          backgroundColor: '#FEF2F2', 
          color: '#B91C1C', 
          padding: '1rem', 
          borderRadius: '0.25rem',
          marginBottom: '1rem'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {result && (
        <div style={{ 
          backgroundColor: '#F0FDF4', 
          padding: '1rem', 
          borderRadius: '0.25rem' 
        }}>
          <h3 style={{ fontSize: '1.25rem', marginBottom: '0.5rem' }}>Results:</h3>
          <pre style={{ 
            backgroundColor: '#ECFDF5', 
            padding: '0.5rem', 
            borderRadius: '0.25rem',
            overflowX: 'auto'
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
