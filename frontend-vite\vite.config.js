import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(),
    {
      name: 'handle-html-requests',
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          // Check if the request is for a specific HTML file
          if (req.url === '/api/test-simple.html') {
            // Serve the file directly
            const filePath = path.resolve(__dirname, 'public/api/test-simple.html');
            if (fs.existsSync(filePath)) {
              const content = fs.readFileSync(filePath, 'utf-8');
              res.setHeader('Content-Type', 'text/html');
              res.end(content);
              return;
            }
          } else if (req.url === '/test-simple.html') {
            // Serve the file directly
            const filePath = path.resolve(__dirname, 'public/test-simple.html');
            if (fs.existsSync(filePath)) {
              const content = fs.readFileSync(filePath, 'utf-8');
              res.setHeader('Content-Type', 'text/html');
              res.end(content);
              return;
            }
          }
          next();
        });
      }
    }
  ],
  server: {
    port: 7000,
    strictPort: true, // Don't try other ports if 7000 is in use
    hmr: {
      overlay: false,
    },
    proxy: {
      // Proxy API requests to avoid CORS issues
      '/api': {
        target: 'http://localhost:7001',
        changeOrigin: true,
        secure: false,
        bypass: (req, res, options) => {
          // Don't proxy HTML files in the /api path
          if (req.url.endsWith('.html')) {
            return req.url;
          }
        }
      },
      '/health': {
        target: 'http://localhost:7001',
        changeOrigin: true,
        secure: false,
      },
      '/.VibeArch': {
        target: 'http://localhost:7001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
})
