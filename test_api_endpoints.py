#!/usr/bin/env python3

import requests
import json
import os
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:7001/api"

def test_project_check():
    """Test the project check endpoint"""
    test_path = r'C:\Users\<USER>\Documents\VSCode\Vibearch\test_project_setup'
    
    print(f"Testing project check for path: {test_path}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/projects/check",
            json={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"Error Response: {response.text}")
            return None
    except Exception as e:
        print(f"Error testing project check: {e}")
        return None

def test_project_initialize():
    """Test the project initialize endpoint"""
    test_path = r'C:\Users\<USER>\Documents\VSCode\Vibearch\test_new_project'
    
    print(f"\nTesting project initialize for path: {test_path}")
    
    # Clean up if directory exists
    if os.path.exists(test_path):
        import shutil
        shutil.rmtree(test_path)
        print(f"Cleaned up existing directory: {test_path}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/projects/initialize",
            json={"path": test_path, "name": "Test New Project"},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            # Check if .VibeArch directory was created
            vibearch_dir = os.path.join(test_path, ".VibeArch")
            print(f"Created .VibeArch directory: {os.path.exists(vibearch_dir)}")
            
            return result
        else:
            print(f"Error Response: {response.text}")
            return None
    except Exception as e:
        print(f"Error testing project initialize: {e}")
        return None

def test_project_load():
    """Test the project load endpoint"""
    test_path = r'C:\Users\<USER>\Documents\VSCode\Vibearch\test_project_setup'
    
    print(f"\nTesting project load for path: {test_path}")
    
    try:
        response = requests.get(
            f"{BASE_URL}/projects/load",
            params={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"Error Response: {response.text}")
            return None
    except Exception as e:
        print(f"Error testing project load: {e}")
        return None

def test_nonexistent_project_check():
    """Test the project check endpoint with a non-existent directory"""
    test_path = r'C:\Users\<USER>\Documents\VSCode\Vibearch\nonexistent_project'
    
    print(f"\nTesting project check for non-existent path: {test_path}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/projects/check",
            json={"path": test_path},
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"Error Response: {response.text}")
            return None
    except Exception as e:
        print(f"Error testing project check: {e}")
        return None

if __name__ == "__main__":
    print("Testing API endpoints...")
    
    # Test check endpoint with existing project
    check_result = test_project_check()
    
    # Test check endpoint with non-existent project
    nonexistent_check_result = test_nonexistent_project_check()
    
    # Test initialize endpoint
    init_result = test_project_initialize()
    
    # Test load endpoint
    load_result = test_project_load()
    
    print("\nTest completed!") 